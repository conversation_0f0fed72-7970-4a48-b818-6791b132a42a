import { PriceData } from '../../../../shared/types'
import { TradingStrategy, TradingDecision } from './TradingStrategy'

interface ThresholdAnalysis {
	changePercent: number
	volatility: number
	momentum: 'strong' | 'moderate' | 'weak'
	consistency: number // 0-1 scale
	direction: 'up' | 'down' | 'neutral'
}

export class ThresholdStrategy extends TradingStrategy {
	private lastTradeTime: number = 0
	private readonly COOLDOWN_PERIOD = 30000 // 30 seconds cooldown
	private volatilityHistory: number[] = []

	getName(): string {
		return 'Enhanced Threshold Strategy'
	}

	getDescription(): string {
		return 'Advanced threshold strategy with momentum analysis, volatility filtering, and trend consistency checks'
	}

	async evaluate(priceData: PriceData): Promise<TradingDecision> {
		// Add current price data to history
		this.addPriceData(priceData)

		// Configuration with enhanced defaults
		const threshold = this.config.threshold || 0.02
		const minConfidence = this.config.minConfidence || 0.6
		const volatilityFilter = this.config.volatilityFilter !== false // Default true
		const momentumConfirmation = this.config.momentumConfirmation !== false // Default true
		const consistencyCheck = this.config.consistencyCheck !== false // Default true

		// Check cooldown period
		const now = Date.now()
		if (now - this.lastTradeTime < this.COOLDOWN_PERIOD) {
			return {
				shouldTrade: false,
				reason: `Cooldown period active (${Math.round(
					(this.COOLDOWN_PERIOD - (now - this.lastTradeTime)) / 1000
				)}s remaining)`
			}
		}

		// Perform comprehensive threshold analysis
		const analysis = this.analyzeThreshold(priceData, threshold)

		// Apply volatility filter
		if (volatilityFilter && !this.passesVolatilityFilter(analysis)) {
			return {
				shouldTrade: false,
				reason: `Price change ${analysis.changePercent.toFixed(
					2
				)}% exceeds threshold but volatility too high (${analysis.volatility.toFixed(3)})`
			}
		}

		// Check basic threshold condition
		if (Math.abs(analysis.changePercent) < threshold) {
			return {
				shouldTrade: false,
				reason: `Price change ${analysis.changePercent.toFixed(2)}% below threshold ${threshold}%`
			}
		}

		// Generate trading decision with enhanced logic
		const decision = this.generateTradingDecision(
			analysis,
			threshold,
			minConfidence,
			momentumConfirmation,
			consistencyCheck
		)

		// Update last trade time if we're making a trade
		if (decision.shouldTrade) {
			this.lastTradeTime = now
		}

		return decision
	}

	/**
	 * Analyze threshold conditions with momentum and volatility
	 */
	private analyzeThreshold(priceData: PriceData, threshold: number): ThresholdAnalysis {
		const changePercent = priceData.changePercent

		// Calculate volatility
		const volatility = this.calculateVolatility()
		this.volatilityHistory.push(Math.abs(changePercent))
		if (this.volatilityHistory.length > 20) {
			this.volatilityHistory = this.volatilityHistory.slice(-20)
		}

		// Determine momentum strength
		let momentum: 'strong' | 'moderate' | 'weak' = 'weak'
		const absChange = Math.abs(changePercent)
		if (absChange >= threshold * 3) momentum = 'strong'
		else if (absChange >= threshold * 1.5) momentum = 'moderate'

		// Calculate trend consistency
		const consistency = this.calculateTrendConsistency(priceData.trend)

		return {
			changePercent,
			volatility,
			momentum,
			consistency,
			direction: priceData.trend
		}
	}

	/**
	 * Calculate market volatility
	 */
	private calculateVolatility(): number {
		if (this.volatilityHistory.length < 5) return 0

		const recentVolatility = this.volatilityHistory.slice(-10)
		const mean = recentVolatility.reduce((sum, vol) => sum + vol, 0) / recentVolatility.length
		const variance = recentVolatility.reduce((sum, vol) => sum + Math.pow(vol - mean, 2), 0) / recentVolatility.length
		return Math.sqrt(variance)
	}

	/**
	 * Calculate trend consistency over recent periods
	 */
	private calculateTrendConsistency(currentTrend: 'up' | 'down' | 'neutral'): number {
		if (this.priceHistory.length < 5) return 0.5

		const recentTrends = this.priceHistory.slice(-5).map(p => p.trend)
		const consistentTrends = recentTrends.filter(trend => trend === currentTrend).length
		return consistentTrends / recentTrends.length
	}

	/**
	 * Check if the signal passes volatility filter
	 */
	private passesVolatilityFilter(analysis: ThresholdAnalysis): boolean {
		// Don't trade in extremely volatile conditions
		const maxVolatility = 0.05 // 5% maximum volatility
		return analysis.volatility <= maxVolatility
	}

	/**
	 * Generate comprehensive trading decision
	 */
	private generateTradingDecision(
		analysis: ThresholdAnalysis,
		threshold: number,
		minConfidence: number,
		momentumConfirmation: boolean,
		consistencyCheck: boolean
	): TradingDecision {
		const direction = analysis.direction === 'up' ? 'high' : 'low'

		// Base confidence from threshold exceedance
		let confidence = Math.min(Math.abs(analysis.changePercent) / threshold, 2) / 2 // Cap at 2x threshold

		// Momentum modifier
		if (momentumConfirmation) {
			if (analysis.momentum === 'strong') {
				confidence += 0.2
			} else if (analysis.momentum === 'moderate') {
				confidence += 0.1
			} else {
				confidence -= 0.1 // Weak momentum reduces confidence
			}
		}

		// Consistency modifier
		if (consistencyCheck) {
			confidence += (analysis.consistency - 0.5) * 0.3 // -0.15 to +0.15 based on consistency
		}

		// Volatility modifier (lower volatility = higher confidence)
		const volatilityModifier = Math.max(0, (0.05 - analysis.volatility) * 2) // 0 to 0.1 bonus
		confidence += volatilityModifier

		// Ensure confidence is within bounds
		confidence = Math.max(0, Math.min(1, confidence))

		// Build detailed reason
		let reason = `Price change ${analysis.changePercent.toFixed(2)}% exceeds threshold ${threshold}%`
		reason += ` (momentum: ${analysis.momentum}, consistency: ${(analysis.consistency * 100).toFixed(0)}%)`
		reason += ` - confidence: ${confidence.toFixed(2)}`

		// Final decision
		const shouldTrade = confidence >= minConfidence

		if (!shouldTrade) {
			return {
				shouldTrade: false,
				reason: `${reason} - below minimum confidence ${minConfidence}`
			}
		}

		return {
			shouldTrade: true,
			direction,
			confidence,
			reason
		}
	}
}
