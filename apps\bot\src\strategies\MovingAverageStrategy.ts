import { PriceData } from '../../../../shared/types'
import { TradingStrategy, TradingDecision } from './TradingStrategy'

interface MAAnalysis {
	shortMA: number
	longMA: number
	previousShortMA: number
	previousLongMA: number
	crossover: 'bullish' | 'bearish' | 'none'
	crossoverStrength: number // 0-1 scale
	trend: 'uptrend' | 'downtrend' | 'sideways'
	pricePosition: 'above_both' | 'below_both' | 'between' | 'mixed'
	convergence: 'converging' | 'diverging' | 'stable'
}

export class MovingAverageStrategy extends TradingStrategy {
	private lastTradeTime = 0 // Time of last trade (ms)
	private readonly COOLDOWN_PERIOD = 45000 // 45 seconds cooldown
	private maHistory: Array<{ short: number; long: number }> = [] // MA history for convergence analysis

	getName(): string {
		return 'Enhanced Moving Average Crossover'
	}

	getDescription(): string {
		return 'Advanced MA strategy with crossover detection, trend analysis, and convergence/divergence patterns'
	}

	async evaluate(priceData: PriceData): Promise<TradingDecision> {
		// Add current price data to history
		this.addPriceData(priceData)

		// Configuration with enhanced defaults
		const shortPeriod = this.config.shortPeriod ?? 5
		const longPeriod = this.config.longPeriod ?? 20
		const minConfidence = this.config.minConfidence ?? 0.6
		const crossoverOnly = this.config.crossoverOnly ?? false
		const convergenceFilter = this.config.convergenceFilter ?? true

		// Check cooldown period
		const now = Date.now()
		if (now - this.lastTradeTime < this.COOLDOWN_PERIOD) {
			return {
				shouldTrade: false,
				reason: `Cooldown period active (${Math.round(
					(this.COOLDOWN_PERIOD - (now - this.lastTradeTime)) / 1000
				)}s remaining)`
			}
		}

		// Calculate moving averages
		const shortMA = this.getSimpleMovingAverage(shortPeriod)
		const longMA = this.getSimpleMovingAverage(longPeriod)

		if (shortMA === null || longMA === null) {
			return {
				shouldTrade: false,
				reason: `Insufficient data for moving averages (need ${longPeriod} data points)`
			}
		}

		// Get previous MAs for crossover detection
		const previousShortMA = this.getPreviousMA(shortPeriod)
		const previousLongMA = this.getPreviousMA(longPeriod)

		// Update MA history
		this.maHistory.push({ short: shortMA, long: longMA })
		if (this.maHistory.length > 50) {
			this.maHistory = this.maHistory.slice(-50)
		}

		// Perform comprehensive MA analysis
		const analysis = this.analyzeMovingAverages(priceData, shortMA, longMA, previousShortMA, previousLongMA)

		// Generate trading decision
		const decision = this.generateTradingDecision(analysis, priceData, minConfidence, crossoverOnly, convergenceFilter)

		// Update last trade time if we're making a trade
		if (decision.shouldTrade) {
			this.lastTradeTime = now
		}

		return decision
	}

	/**
	 * Get previous moving average for crossover detection
	 */
	private getPreviousMA(period: number): number | null {
		if (this.priceHistory.length < period + 1) return null

		const previousPrices = this.priceHistory.slice(-(period + 1), -1).map(p => p.current)
		if (previousPrices.length < period) return null

		const sum = previousPrices.reduce((acc, price) => acc + price, 0)
		return sum / period
	}

	/**
	 * Analyze moving average patterns and relationships
	 */
	private analyzeMovingAverages(
		priceData: PriceData,
		shortMA: number,
		longMA: number,
		previousShortMA: number | null,
		previousLongMA: number | null
	): MAAnalysis {
		const currentPrice = priceData.current

		// Detect crossovers
		let crossover: 'bullish' | 'bearish' | 'none' = 'none'
		let crossoverStrength = 0

		if (previousShortMA !== null && previousLongMA !== null) {
			// Bullish crossover: short MA crosses above long MA
			if (longMA !== 0 && previousShortMA <= previousLongMA && shortMA > longMA) {
				crossover = 'bullish'
				crossoverStrength = Math.min((shortMA - longMA) / longMA, 0.1) * 10 // 0-1 scale
			}
			// Bearish crossover: short MA crosses below long MA
			else if (previousShortMA >= previousLongMA && shortMA < longMA) {
				crossover = 'bearish'
				crossoverStrength = Math.min((longMA - shortMA) / longMA, 0.1) * 10 // 0-1 scale
			}
		}

		// Determine trend
		let trend: 'uptrend' | 'downtrend' | 'sideways' = 'sideways'
		if (shortMA > longMA && currentPrice > shortMA) trend = 'uptrend'
		else if (shortMA < longMA && currentPrice < shortMA) trend = 'downtrend'

		// Determine price position relative to MAs
		let pricePosition: 'above_both' | 'below_both' | 'between' | 'mixed' = 'mixed'
		if (currentPrice > shortMA && currentPrice > longMA) pricePosition = 'above_both'
		else if (currentPrice < shortMA && currentPrice < longMA) pricePosition = 'below_both'
		else if ((currentPrice > shortMA && currentPrice < longMA) || (currentPrice < shortMA && currentPrice > longMA)) {
			pricePosition = 'between'
		}

		// Analyze convergence/divergence
		let convergence: 'converging' | 'diverging' | 'stable' = 'stable'
		if (this.maHistory.length >= 3) {
			const recent = this.maHistory.slice(-3)
			const spreads = recent.map(ma => Math.abs(ma.short - ma.long))

			if (spreads[2] < spreads[0] * 0.9) convergence = 'converging'
			else if (spreads[2] > spreads[0] * 1.1) convergence = 'diverging'
		}

		return {
			shortMA,
			longMA,
			previousShortMA: previousShortMA || shortMA,
			previousLongMA: previousLongMA || longMA,
			crossover,
			crossoverStrength,
			trend,
			pricePosition,
			convergence
		}
	}

	/**
	 * Generate comprehensive trading decision
	 */
	private generateTradingDecision(
		analysis: MAAnalysis,
		_priceData: PriceData,
		minConfidence: number,
		crossoverOnly: boolean,
		convergenceFilter: boolean
	): TradingDecision {
		let shouldTrade = false
		let direction: 'high' | 'low' | undefined
		let confidence = 0
		let reason = ''

		// Primary signal: Crossovers
		if (analysis.crossover === 'bullish') {
			shouldTrade = true
			direction = 'high'
			confidence = 0.7 + analysis.crossoverStrength * 0.3
			reason = `Bullish MA crossover (strength: ${analysis.crossoverStrength.toFixed(2)})`
		} else if (analysis.crossover === 'bearish') {
			shouldTrade = true
			direction = 'low'
			confidence = 0.7 + analysis.crossoverStrength * 0.3
			reason = `Bearish MA crossover (strength: ${analysis.crossoverStrength.toFixed(2)})`
		}

		// Secondary signals: Trend following (if crossoverOnly is false)
		if (!crossoverOnly && analysis.crossover === 'none') {
			if (analysis.trend === 'uptrend' && analysis.pricePosition === 'above_both') {
				shouldTrade = true
				direction = 'high'
				confidence = 0.6
				reason = `Strong uptrend - price above both MAs`
			} else if (analysis.trend === 'downtrend' && analysis.pricePosition === 'below_both') {
				shouldTrade = true
				direction = 'low'
				confidence = 0.6
				reason = `Strong downtrend - price below both MAs`
			}
		}

		// Apply convergence filter
		if (convergenceFilter && shouldTrade) {
			if (analysis.convergence === 'converging') {
				confidence -= 0.1
				reason += ` (MAs converging - reduced confidence)`
			} else if (analysis.convergence === 'diverging') {
				confidence += 0.1
				reason += ` (MAs diverging - increased confidence)`
			}
		}

		// Ensure confidence is within bounds
		confidence = Math.max(0, Math.min(1, confidence))

		// Final decision
		if (!shouldTrade || confidence < minConfidence) {
			return {
				shouldTrade: false,
				reason: shouldTrade
					? `${reason} - confidence ${confidence.toFixed(2)} below minimum ${minConfidence}`
					: `No clear MA signal (Short: ${analysis.shortMA.toFixed(4)}, Long: ${analysis.longMA.toFixed(4)}, Trend: ${
							analysis.trend
					  })`
			}
		}

		return {
			shouldTrade: true,
			direction,
			confidence,
			reason: `${reason} - confidence: ${confidence.toFixed(2)}`
		}
	}
}
