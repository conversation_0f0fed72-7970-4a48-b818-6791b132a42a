import { EventEmitter } from 'events'
import { Page } from 'playwright'
import { PriceData } from '../../../../shared/types'

export interface PriceFeedConfig {
	reconnectInterval: number
	maxReconnectAttempts: number
	priceValidationRange: { min: number; max: number }
	enableLogging: boolean
}

export class WebSocketPriceFeed extends EventEmitter {
	private page: Page
	private config: PriceFeedConfig
	private isConnected: boolean = false
	private reconnectAttempts: number = 0
	private reconnectTimer?: NodeJS.Timeout
	private lastPrice: number = 0
	private priceBuffer: number[] = []
	private wsInjected: boolean = false

	constructor(page: Page, config: Partial<PriceFeedConfig> = {}) {
		super()
		this.page = page
		this.config = {
			reconnectInterval: 5000,
			maxReconnectAttempts: 10,
			priceValidationRange: { min: 0.001, max: 1000000 },
			enableLogging: true,
			...config
		}
	}

	async initialize(): Promise<boolean> {
		try {
			await this.injectWebSocketInterceptor()
			await this.setupPriceExtraction()
			this.log('WebSocket price feed initialized')
			return true
		} catch (error) {
			this.log(`Failed to initialize WebSocket price feed: ${error}`)
			return false
		}
	}

	private async injectWebSocketInterceptor(): Promise<void> {
		if (this.wsInjected) return

		await this.page.addInitScript(() => {
			// @ts-ignore - Browser context
			const OriginalWebSocket = window.WebSocket

			// Price extraction function (defined outside class to avoid 'this' issues)
			function extractPriceFromMessage(data: any): number {
				// Common price field names in trading platforms
				const priceFields = [
					'price',
					'currentPrice',
					'lastPrice',
					'quote',
					'rate',
					'bid',
					'ask',
					'close',
					'value',
					'amount'
				]

				// Direct price fields
				for (const field of priceFields) {
					if (data[field] && typeof data[field] === 'number' && data[field] > 0) {
						return data[field]
					}
				}

				// Nested price data
				if (data.data) {
					for (const field of priceFields) {
						if (data.data[field] && typeof data.data[field] === 'number' && data.data[field] > 0) {
							return data.data[field]
						}
					}
				}

				// Array of price data
				if (Array.isArray(data) && data.length > 0) {
					const lastItem = data[data.length - 1]
					if (typeof lastItem === 'number' && lastItem > 0) {
						return lastItem
					}
					if (typeof lastItem === 'object') {
						for (const field of priceFields) {
							if (lastItem[field] && typeof lastItem[field] === 'number' && lastItem[field] > 0) {
								return lastItem[field]
							}
						}
					}
				}

				// OHLC data
				if (data.ohlc || data.candle) {
					const ohlc = data.ohlc || data.candle
					return ohlc.close || ohlc.c || ohlc[3] // close price
				}

				return 0
			}

			// Create intercepted WebSocket
			// @ts-ignore - Browser context
			window.WebSocket = class extends OriginalWebSocket {
				constructor(url: string | URL, protocols?: string | string[]) {
					super(url, protocols)

					// Store reference for price extraction
					// @ts-ignore - Browser context
					;(window as any)._tradingWebSocket = this

					// Intercept messages
					// @ts-ignore - Browser context addEventListener exists
					this.addEventListener('message', (event: any) => {
						try {
							const data = JSON.parse(event.data)

							// Look for price data in various formats
							const price = extractPriceFromMessage(data)
							if (price > 0) {
								// @ts-ignore - Browser context
								;(window as any)._latestPrice = price
								// @ts-ignore - Browser context
								;(window as any)._lastPriceUpdate = Date.now()

								// Dispatch custom event
								// @ts-ignore - Browser context
								window.dispatchEvent(
									// @ts-ignore - Browser context
									new CustomEvent('priceUpdate', {
										detail: { price, timestamp: Date.now(), source: 'websocket' }
									})
								)
							}
						} catch (e) {
							// Ignore parsing errors
						}
					})
				}
			}
		})

		this.wsInjected = true
	}

	private async setupPriceExtraction(): Promise<void> {
		// Listen for custom price update events
		await this.page.evaluate(() => {
			// @ts-ignore - Browser context
			window.addEventListener('priceUpdate', (event: any) => {
				const { price, timestamp, source } = event.detail
				// @ts-ignore - Browser context
				;(window as any)._priceUpdateCallback?.(price, timestamp, source)
			})
		})

		// Set up price extraction callback
		await this.page.exposeFunction('_priceUpdateCallback', (price: number, timestamp: number, source: string) => {
			this.handlePriceUpdate(price, timestamp, source)
		})

		// Start polling for price updates
		this.startPricePolling()
	}

	private startPricePolling(): void {
		const pollInterval = setInterval(async () => {
			try {
				const priceData = await this.extractLatestPrice()
				if (priceData) {
					this.handlePriceUpdate(priceData.price, priceData.timestamp, priceData.source)
				}
			} catch (error) {
				this.log(`Price polling error: ${error}`)
			}
		}, 1000) // Poll every second

		// Clean up on page close
		this.page.on('close', () => {
			clearInterval(pollInterval)
		})
	}

	private async extractLatestPrice(): Promise<{ price: number; timestamp: number; source: string } | null> {
		try {
			const result = await this.page.evaluate(() => {
				// @ts-ignore - Browser context
				const win = window as any

				// Check for WebSocket price
				if (win._latestPrice && win._lastPriceUpdate) {
					const age = Date.now() - win._lastPriceUpdate
					if (age < 10000) {
						// Price is less than 10 seconds old
						return {
							price: win._latestPrice,
							timestamp: win._lastPriceUpdate,
							source: 'websocket'
						}
					}
				}

				// Fallback to DOM extraction
				const priceSelectors = [
					'.current-price',
					'.live-price',
					'.asset-price',
					'.quote-price',
					'[data-price]',
					'.price-display'
				]

				for (const selector of priceSelectors) {
					// @ts-ignore - Browser context
					const element = document.querySelector(selector)
					if (element) {
						const text = element.textContent || element.getAttribute('data-price')
						if (text) {
							const price = parseFloat(text.replace(/[^\d.-]/g, ''))
							if (!isNaN(price) && price > 0) {
								return {
									price,
									timestamp: Date.now(),
									source: 'dom'
								}
							}
						}
					}
				}

				return null
			})

			return result
		} catch (error) {
			this.log(`Error extracting latest price: ${error}`)
			return null
		}
	}

	private handlePriceUpdate(price: number, timestamp: number, source: string): void {
		// Validate price
		if (!this.isValidPrice(price)) {
			this.log(`Invalid price received: ${price}`)
			return
		}

		// Smooth price using buffer
		this.priceBuffer.push(price)
		if (this.priceBuffer.length > 5) {
			this.priceBuffer.shift()
		}

		const smoothedPrice = this.smoothPrice()

		// Create price data object
		const priceData: PriceData = {
			current: smoothedPrice,
			previous: this.lastPrice,
			timestamp,
			trend: smoothedPrice > this.lastPrice ? 'up' : smoothedPrice < this.lastPrice ? 'down' : 'neutral',
			change: smoothedPrice - this.lastPrice,
			changePercent: this.lastPrice > 0 ? ((smoothedPrice - this.lastPrice) / this.lastPrice) * 100 : 0
		}

		this.lastPrice = smoothedPrice
		this.isConnected = true
		this.reconnectAttempts = 0

		// Emit price update
		this.emit('priceUpdate', priceData)
		this.log(`Price update: ${smoothedPrice} (${source})`)
	}

	private isValidPrice(price: number): boolean {
		return (
			typeof price === 'number' &&
			!isNaN(price) &&
			isFinite(price) &&
			price >= this.config.priceValidationRange.min &&
			price <= this.config.priceValidationRange.max
		)
	}

	private smoothPrice(): number {
		if (this.priceBuffer.length === 0) return 0
		if (this.priceBuffer.length === 1) return this.priceBuffer[0]

		// Use median for smoothing to reduce noise
		const sorted = [...this.priceBuffer].sort((a, b) => a - b)
		const mid = Math.floor(sorted.length / 2)

		return sorted.length % 2 === 0 ? (sorted[mid - 1] + sorted[mid]) / 2 : sorted[mid]
	}

	async reconnect(): Promise<void> {
		if (this.reconnectAttempts >= this.config.maxReconnectAttempts) {
			this.log('Max reconnection attempts reached')
			this.emit('maxReconnectAttemptsReached')
			return
		}

		this.reconnectAttempts++
		this.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.config.maxReconnectAttempts})`)

		try {
			await this.initialize()
			this.log('Reconnection successful')
			this.emit('reconnected')
		} catch (error) {
			this.log(`Reconnection failed: ${error}`)

			// Schedule next reconnection attempt
			this.reconnectTimer = setTimeout(() => {
				this.reconnect()
			}, this.config.reconnectInterval)
		}
	}

	disconnect(): void {
		this.isConnected = false
		if (this.reconnectTimer) {
			clearTimeout(this.reconnectTimer)
			this.reconnectTimer = undefined
		}
		this.log('WebSocket price feed disconnected')
		this.emit('disconnected')
	}

	getConnectionStatus(): boolean {
		return this.isConnected
	}

	getLastPrice(): number {
		return this.lastPrice
	}

	private log(message: string): void {
		if (this.config.enableLogging) {
			console.log(`[WebSocketPriceFeed] ${message}`)
		}
	}
}
