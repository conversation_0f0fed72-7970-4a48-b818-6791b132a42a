import { useState, useEffect } from 'react'
import type { BotStatus, PriceData, TradeResult, StrategyType } from '../../../../shared/types'

interface CompactTradingDashboardProps {
	botStatus: BotStatus
	currentPrice: PriceData | null
	recentTrades: TradeResult[]
	onStartBot: () => void
	onStopBot: () => void
	statusMessage?: string
	browserStatus: { initialized: boolean; loggedIn: boolean }
	onInitializeBrowser: () => void
	onCheckLoginStatus: () => void
	onCloseBrowser: () => void
	onUpdateSettings: (settings: any) => void
	currentSettings: any
}

export function CompactTradingDashboard({
	botStatus,
	currentPrice,
	recentTrades,
	onStartBot,
	onStopBot,
	statusMessage,
	browserStatus,
	onInitializeBrowser,
	onCheckLoginStatus,
	onCloseBrowser,
	onUpdateSettings,
	currentSettings
}: CompactTradingDashboardProps) {
	const [selectedStrategy, setSelectedStrategy] = useState<StrategyType>(currentSettings?.strategy || 'threshold')
	const [tradeCapital, setTradeCapital] = useState(currentSettings?.tradeAmount || 100)
	const [targetProfit, setTargetProfit] = useState(currentSettings?.takeProfit || 1000)
	const [showCongratulations, setShowCongratulations] = useState(false)
	const [profitMessage, setProfitMessage] = useState('')

	// Check for profit target achievement
	useEffect(() => {
		if (botStatus.totalProfit >= targetProfit && targetProfit > 0) {
			setProfitMessage(`Congratulations, you have earned $${botStatus.totalProfit.toFixed(2)}!`)
			setShowCongratulations(true)
			onStopBot() // Auto-stop when target is reached
		}
	}, [botStatus.totalProfit, targetProfit, onStopBot])

	const formatPrice = (price: number) => price.toFixed(5)
	const formatProfit = (profit: number) => `${profit >= 0 ? '+' : ''}$${profit.toFixed(2)}`
	const formatPercent = (percent: number) => `${percent >= 0 ? '+' : ''}${percent.toFixed(1)}%`

	const getTrendIcon = (trend?: string) => {
		switch (trend) {
			case 'up': return '↗'
			case 'down': return '↘'
			default: return '→'
		}
	}

	const getTrendColor = (trend?: string) => {
		switch (trend) {
			case 'up': return 'text-green-400'
			case 'down': return 'text-red-400'
			default: return 'text-gray-400'
		}
	}

	const handleStrategyChange = (strategy: StrategyType) => {
		setSelectedStrategy(strategy)
		onUpdateSettings({
			...currentSettings,
			strategy,
			strategyConfig: getDefaultStrategyConfig(strategy)
		})
	}

	const getDefaultStrategyConfig = (strategy: StrategyType) => {
		switch (strategy) {
			case 'oscillate':
				return { rangeThreshold: 0.5, minOscillations: 3, confidenceThreshold: 0.7 }
			case 'slide':
				return { trendThreshold: 0.2, dynamicStopLoss: true, minTrendStrength: 0.6 }
			case 'ai':
				return { aiProvider: 'openai', minConfidence: 0.7, analysisInterval: 60 }
			default:
				return { threshold: 0.02 }
		}
	}

	const canStartBot = browserStatus.initialized && browserStatus.loggedIn && !botStatus.isRunning

	return (
		<div className="bg-gray-900 text-white p-4 rounded-lg shadow-2xl max-w-md mx-auto">
			{/* Congratulations Modal */}
			{showCongratulations && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
					<div className="bg-green-800 p-6 rounded-lg text-center">
						<h2 className="text-2xl font-bold text-green-300 mb-4">🎉 Success!</h2>
						<p className="text-green-200 mb-4">{profitMessage}</p>
						<button
							onClick={() => setShowCongratulations(false)}
							className="px-4 py-2 bg-green-600 hover:bg-green-700 rounded text-white"
						>
							Continue
						</button>
					</div>
				</div>
			)}

			{/* Header */}
			<div className="flex items-center justify-between mb-4">
				<h1 className="text-lg font-bold">Dewbot Pro</h1>
				<div className="flex gap-2">
					<div className={`w-3 h-3 rounded-full ${browserStatus.initialized ? 'bg-green-500' : 'bg-red-500'}`} />
					<div className={`w-3 h-3 rounded-full ${botStatus.isRunning ? 'bg-blue-500' : 'bg-gray-500'}`} />
				</div>
			</div>

			{/* Strategy Selection */}
			<div className="mb-4">
				<label className="block text-sm font-medium mb-2">Strategy</label>
				<div className="flex gap-2 mb-2">
					<button
						onClick={() => handleStrategyChange('oscillate')}
						className={`px-3 py-1 rounded text-xs ${
							selectedStrategy === 'oscillate' ? 'bg-blue-600' : 'bg-gray-700 hover:bg-gray-600'
						}`}
					>
						Oscillate
					</button>
					<button
						onClick={() => handleStrategyChange('slide')}
						className={`px-3 py-1 rounded text-xs ${
							selectedStrategy === 'slide' ? 'bg-blue-600' : 'bg-gray-700 hover:bg-gray-600'
						}`}
					>
						Slide
					</button>
					<button
						onClick={() => handleStrategyChange('ai')}
						className={`px-3 py-1 rounded text-xs ${
							selectedStrategy === 'ai' ? 'bg-purple-600' : 'bg-gray-700 hover:bg-gray-600'
						}`}
					>
						AI
					</button>
				</div>
			</div>

			{/* Settings */}
			<div className="grid grid-cols-2 gap-3 mb-4">
				<div>
					<label className="block text-xs text-gray-400 mb-1">Trade Capital</label>
					<input
						type="number"
						value={tradeCapital}
						onChange={(e) => {
							setTradeCapital(Number(e.target.value))
							onUpdateSettings({ ...currentSettings, tradeAmount: Number(e.target.value) })
						}}
						className="w-full px-2 py-1 bg-gray-800 border border-gray-600 rounded text-sm"
						min="1"
					/>
				</div>
				<div>
					<label className="block text-xs text-gray-400 mb-1">Target Profit</label>
					<input
						type="number"
						value={targetProfit}
						onChange={(e) => setTargetProfit(Number(e.target.value))}
						className="w-full px-2 py-1 bg-gray-800 border border-gray-600 rounded text-sm"
						min="1"
					/>
				</div>
			</div>

			{/* Current Price */}
			<div className="bg-gray-800 rounded p-3 mb-4">
				<div className="flex items-center justify-between">
					<span className="text-sm text-gray-400">Current Price</span>
					{currentPrice && (
						<span className={`text-xs ${getTrendColor(currentPrice.trend)}`}>
							{getTrendIcon(currentPrice.trend)} {formatPercent(currentPrice.changePercent)}
						</span>
					)}
				</div>
				<div className="text-xl font-bold">
					{currentPrice ? formatPrice(currentPrice.current) : formatPrice(botStatus.currentPrice)}
				</div>
			</div>

			{/* Stats */}
			<div className="grid grid-cols-3 gap-2 mb-4">
				<div className="bg-gray-800 rounded p-2 text-center">
					<div className="text-lg font-bold text-yellow-400">${botStatus.accountBalance.toFixed(0)}</div>
					<div className="text-xs text-gray-400">Balance</div>
				</div>
				<div className="bg-gray-800 rounded p-2 text-center">
					<div className="text-lg font-bold text-blue-400">{botStatus.tradesCount}</div>
					<div className="text-xs text-gray-400">Trades</div>
				</div>
				<div className="bg-gray-800 rounded p-2 text-center">
					<div className={`text-lg font-bold ${botStatus.totalProfit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
						{formatProfit(botStatus.totalProfit)}
					</div>
					<div className="text-xs text-gray-400">Profit</div>
				</div>
			</div>

			{/* Win Rate */}
			<div className="bg-gray-800 rounded p-2 mb-4">
				<div className="flex justify-between items-center">
					<span className="text-sm">Win Rate</span>
					<span className="text-green-400 font-bold">{botStatus.winRate.toFixed(1)}%</span>
				</div>
				<div className="w-full bg-gray-700 rounded-full h-2 mt-1">
					<div
						className="bg-green-500 h-2 rounded-full transition-all duration-300"
						style={{ width: `${botStatus.winRate}%` }}
					/>
				</div>
			</div>

			{/* Control Buttons */}
			<div className="space-y-2">
				{!browserStatus.initialized ? (
					<button
						onClick={onInitializeBrowser}
						className="w-full py-2 bg-blue-600 hover:bg-blue-700 rounded font-medium text-sm"
					>
						Initialize Browser
					</button>
				) : !browserStatus.loggedIn ? (
					<button
						onClick={onCheckLoginStatus}
						className="w-full py-2 bg-yellow-600 hover:bg-yellow-700 rounded font-medium text-sm"
					>
						Check Login Status
					</button>
				) : (
					<div className="flex gap-2">
						<button
							onClick={canStartBot ? onStartBot : onStopBot}
							className={`flex-1 py-2 rounded font-medium text-sm ${
								canStartBot
									? 'bg-green-600 hover:bg-green-700'
									: 'bg-red-600 hover:bg-red-700'
							}`}
						>
							{botStatus.isRunning ? 'Stop' : 'Start'}
						</button>
						<button
							onClick={() => {
								onUpdateSettings({ ...currentSettings, autoTrade: !currentSettings.autoTrade })
							}}
							className={`px-4 py-2 rounded text-sm ${
								currentSettings.autoTrade ? 'bg-orange-600 hover:bg-orange-700' : 'bg-gray-600 hover:bg-gray-700'
							}`}
						>
							{currentSettings.autoTrade ? 'Auto' : 'Manual'}
						</button>
					</div>
				)}
			</div>

			{/* Status Message */}
			{statusMessage && (
				<div className="mt-3 p-2 bg-blue-900 bg-opacity-50 rounded text-xs text-blue-300">
					{statusMessage}
				</div>
			)}

			{/* Recent Trades */}
			{recentTrades.length > 0 && (
				<div className="mt-4">
					<h3 className="text-sm font-medium mb-2">Recent Trades</h3>
					<div className="space-y-1 max-h-32 overflow-y-auto">
						{recentTrades.slice(-3).map(trade => (
							<div key={trade.id} className="flex justify-between items-center text-xs bg-gray-800 rounded p-2">
								<span className={`px-1 rounded ${
									trade.direction === 'high' ? 'bg-green-900 text-green-300' : 'bg-red-900 text-red-300'
								}`}>
									{trade.direction.toUpperCase()}
								</span>
								<span>{formatPrice(trade.entryPrice)}</span>
								<span className={`${
									trade.result === 'win' ? 'text-green-400' : 
									trade.result === 'loss' ? 'text-red-400' : 'text-yellow-400'
								}`}>
									{trade.result.toUpperCase()}
								</span>
								{trade.profit && (
									<span className={trade.profit >= 0 ? 'text-green-400' : 'text-red-400'}>
										{formatProfit(trade.profit)}
									</span>
								)}
							</div>
						))}
					</div>
				</div>
			)}
		</div>
	)
}
