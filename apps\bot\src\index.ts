import { TradingBot } from './TradingBot'
import { BotConfig, TradingSettings } from '../../../shared/types'

export { TradingBot }
export * from '../../../shared/types'

// Factory function to create a bot instance
export function createBot(settings: TradingSettings, options?: Partial<BotConfig>): TradingBot {
	// Ensure screenshot extraction is enabled by default
	const updatedSettings: TradingSettings = {
		...settings,
		// Make sure screenshot config is always present
		screenshotConfig: {
			enabled: true,
			debugMode: true,
			...(settings.screenshotConfig || {}) // Keep any existing config
		}
	}

	const config: BotConfig = {
		settings: updatedSettings,
		pocketOptionUrl: 'https://pocketoption.com/en/cabinet/demo-quick-high-low/',
		headless: false, // Set to true for production
		userDataDir: undefined, // Can be set to persist browser session
		...options
	}

	return new TradingBot(config)
}

// Default export for easy importing
export default TradingBot
