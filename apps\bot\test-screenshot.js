/**
 * Test script for screenshot-based price extraction
 * This script demonstrates how to use the ScreenshotPriceExtractor independently
 */

const { chromium } = require('playwright')
const { ScreenshotPriceExtractor } = require('./dist/apps/bot/src/ScreenshotPriceExtractor.js')

async function testScreenshotExtraction() {
	console.log('Starting screenshot price extraction test...')

	let browser = null
	let extractor = null

	try {
		// Launch browser
		browser = await chromium.launch({ headless: false })
		const page = await browser.newPage()

		// Navigate to Pocket Option (you should be logged in)
		console.log('Navigating to Pocket Option...')
		await page.goto('https://pocketoption.com/cabinet/demo-quick-high-low/')

		// Wait for page to load
		await page.waitForLoadState('networkidle')
		console.log('Page loaded. Please ensure you are on the trading interface.')

		// Wait a bit for user to navigate if needed
		await page.waitForTimeout(5000)

		// Initialize screenshot extractor with debug mode
		console.log('Initializing screenshot extractor...')
		extractor = new ScreenshotPriceExtractor(page, {
			debugMode: true,
			screenshotPath: './test-screenshots'
		})

		await extractor.initialize()
		console.log('Screenshot extractor initialized successfully.')

		// Test auto-calibration
		console.log('Testing auto-calibration...')
		const optimalArea = await extractor.findOptimalPriceArea()

		if (optimalArea) {
			console.log('✓ Auto-calibration successful!')
			console.log('Optimal price area:', optimalArea)

			// Update configuration with optimal area
			extractor.updateConfig({ priceArea: optimalArea })
		} else {
			console.log('✗ Auto-calibration failed. Using default area.')
		}

		// Test price extraction multiple times
		console.log('\nTesting price extraction (5 attempts)...')

		for (let i = 1; i <= 5; i++) {
			console.log(`\nAttempt ${i}:`)

			const startTime = Date.now()
			const price = await extractor.extractPriceFromScreenshot()
			const duration = Date.now() - startTime

			if (price > 0) {
				console.log(`✓ Price extracted: ${price} (took ${duration}ms)`)
			} else {
				console.log(`✗ Failed to extract price (took ${duration}ms)`)
			}

			// Wait between attempts
			if (i < 5) {
				await page.waitForTimeout(2000)
			}
		}

		console.log('\nTest completed! Check the ./test-screenshots directory for debug images.')
	} catch (error) {
		console.error('Test failed:', error)
	} finally {
		// Cleanup
		if (extractor) {
			await extractor.cleanup()
		}

		if (browser) {
			await browser.close()
		}
	}
}

// Run the test
testScreenshotExtraction().catch(console.error)
