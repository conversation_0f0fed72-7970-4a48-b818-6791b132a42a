import { ThresholdStrategy } from '../strategies/ThresholdStrategy'
import { MovingAverageStrategy } from '../strategies/MovingAverageStrategy'
import { RSIStrategy } from '../strategies/RSIStrategy'
import { MACDStrategy } from '../strategies/MACDStrategy'
import { BollingerBandsStrategy } from '../strategies/BollingerBandsStrategy'
import { PriceData, StrategyConfig } from '../../../../shared/types'

/**
 * Demonstration of all enhanced trading strategies
 */
async function demonstrateAllStrategies() {
	console.log('=== Enhanced Trading Strategies Demonstration ===\n')

	// Generate test price data
	const priceData = generateTestPriceData()

	// Test each strategy
	await testThresholdStrategy(priceData)
	await testMovingAverageStrategy(priceData)
	await testRSIStrategy(priceData)
	await testMACDStrategy(priceData)
	await testBollingerBandsStrategy(priceData)

	console.log('\n=== Summary of Improvements ===')
	console.log('✅ Enhanced Threshold Strategy: Volatility filtering, momentum analysis, trend consistency')
	console.log('✅ Enhanced Moving Average Strategy: Proper crossover detection, convergence analysis')
	console.log('✅ Enhanced RSI Strategy: Wilder\'s RSI, divergence detection, risk management')
	console.log('✅ Enhanced MACD Strategy: Proper EMA calculation, signal line crossovers, divergence')
	console.log('✅ Enhanced Bollinger Bands Strategy: Squeeze detection, volatility breakouts, mean reversion')
	console.log('\nAll strategies now include:')
	console.log('• Cooldown periods to prevent overtrading')
	console.log('• Confidence scoring with minimum thresholds')
	console.log('• Advanced technical analysis')
	console.log('• Risk management features')
	console.log('• Detailed reasoning for each decision')
}

async function testThresholdStrategy(priceData: PriceData[]) {
	console.log('--- Enhanced Threshold Strategy ---')
	
	const config: StrategyConfig = {
		threshold: 0.015, // 1.5%
		minConfidence: 0.6,
		volatilityFilter: true,
		momentumConfirmation: true,
		consistencyCheck: true
	}

	const strategy = new ThresholdStrategy(config)
	
	// Test with significant price movement
	const testData = priceData.find(p => Math.abs(p.changePercent) > 0.02)
	if (testData) {
		const decision = await strategy.evaluate(testData)
		console.log(`Price change: ${testData.changePercent.toFixed(2)}%`)
		console.log(`Decision: ${decision.reason}`)
		if (decision.shouldTrade) {
			console.log(`  → TRADE: ${decision.direction?.toUpperCase()} (Confidence: ${decision.confidence?.toFixed(2)})`)
		}
	}
	console.log()
}

async function testMovingAverageStrategy(priceData: PriceData[]) {
	console.log('--- Enhanced Moving Average Strategy ---')
	
	const config: StrategyConfig = {
		shortPeriod: 5,
		longPeriod: 15,
		minConfidence: 0.6,
		crossoverOnly: false,
		convergenceFilter: true
	}

	const strategy = new MovingAverageStrategy(config)
	
	// Feed enough data for MA calculation
	for (const data of priceData.slice(0, 20)) {
		const decision = await strategy.evaluate(data)
		if (decision.shouldTrade) {
			console.log(`Decision: ${decision.reason}`)
			console.log(`  → TRADE: ${decision.direction?.toUpperCase()} (Confidence: ${decision.confidence?.toFixed(2)})`)
			break
		}
	}
	console.log()
}

async function testRSIStrategy(priceData: PriceData[]) {
	console.log('--- Enhanced RSI Strategy ---')
	
	const config: StrategyConfig = {
		rsiPeriod: 14,
		oversoldLevel: 30,
		overboughtLevel: 70,
		extremeOversoldLevel: 20,
		extremeOverboughtLevel: 80,
		enableDivergence: true,
		minConfidence: 0.6,
		trendConfirmation: true
	}

	const strategy = new RSIStrategy(config)
	
	// Feed enough data for RSI calculation
	for (const data of priceData) {
		const decision = await strategy.evaluate(data)
		if (decision.shouldTrade) {
			console.log(`Decision: ${decision.reason}`)
			console.log(`  → TRADE: ${decision.direction?.toUpperCase()} (Confidence: ${decision.confidence?.toFixed(2)})`)
			break
		}
	}
	console.log()
}

async function testMACDStrategy(priceData: PriceData[]) {
	console.log('--- Enhanced MACD Strategy ---')
	
	const config: StrategyConfig = {
		fastPeriod: 12,
		slowPeriod: 26,
		signalPeriod: 9,
		minConfidence: 0.6,
		enableDivergence: true,
		histogramFilter: true
	}

	const strategy = new MACDStrategy(config)
	
	// Feed enough data for MACD calculation
	for (const data of priceData) {
		const decision = await strategy.evaluate(data)
		if (decision.shouldTrade) {
			console.log(`Decision: ${decision.reason}`)
			console.log(`  → TRADE: ${decision.direction?.toUpperCase()} (Confidence: ${decision.confidence?.toFixed(2)})`)
			break
		}
	}
	console.log()
}

async function testBollingerBandsStrategy(priceData: PriceData[]) {
	console.log('--- Enhanced Bollinger Bands Strategy ---')
	
	const config: StrategyConfig = {
		period: 20,
		standardDeviations: 2,
		minConfidence: 0.6,
		squeezeDetection: true,
		volatilityBreakout: true,
		meanReversion: true
	}

	const strategy = new BollingerBandsStrategy(config)
	
	// Feed enough data for Bollinger Bands calculation
	for (const data of priceData) {
		const decision = await strategy.evaluate(data)
		if (decision.shouldTrade) {
			console.log(`Decision: ${decision.reason}`)
			console.log(`  → TRADE: ${decision.direction?.toUpperCase()} (Confidence: ${decision.confidence?.toFixed(2)})`)
			break
		}
	}
	console.log()
}

/**
 * Generate test price data with various market conditions
 */
function generateTestPriceData(): PriceData[] {
	const prices: PriceData[] = []
	let currentPrice = 100
	
	// Generate 50 periods of price data
	for (let i = 0; i < 50; i++) {
		const previousPrice = currentPrice
		
		// Create different market phases
		let change = 0
		if (i < 15) {
			// Trending up phase
			change = (Math.random() * 2 - 0.3) // Mostly positive
		} else if (i < 30) {
			// Volatile sideways phase
			change = (Math.random() - 0.5) * 3 // High volatility
		} else if (i < 40) {
			// Trending down phase
			change = (Math.random() * 2 - 1.7) // Mostly negative
		} else {
			// Recovery phase
			change = (Math.random() * 1.5 - 0.2) // Mostly positive
		}
		
		currentPrice += change
		currentPrice = Math.max(currentPrice, 50) // Floor price
		
		const priceData: PriceData = {
			current: currentPrice,
			previous: previousPrice,
			timestamp: Date.now() + i * 60000, // 1 minute intervals
			trend: currentPrice > previousPrice ? 'up' : currentPrice < previousPrice ? 'down' : 'neutral',
			change: currentPrice - previousPrice,
			changePercent: previousPrice > 0 ? ((currentPrice - previousPrice) / previousPrice) * 100 : 0
		}
		
		prices.push(priceData)
	}
	
	return prices
}

// Run the demonstration
if (require.main === module) {
	demonstrateAllStrategies().catch(console.error)
}

export { demonstrateAllStrategies }
