import { Page } from 'playwright'
import Tesseract from 'tesseract.js'
import sharp from 'sharp'
import * as fs from 'fs'
import * as path from 'path'

export interface ChartArea {
	x: number
	y: number
	width: number
	height: number
}

export interface PriceExtractionConfig {
	chartArea?: ChartArea
	priceArea?: ChartArea
	ocrOptions?: any // Using any for now due to Tesseract types
	debugMode?: boolean
	screenshotPath?: string
}

export class ScreenshotPriceExtractor {
	private page: Page
	private config: PriceExtractionConfig
	private worker: Tesseract.Worker | null = null
	private isInitialized = false

	constructor(page: Page, config: PriceExtractionConfig = {}) {
		this.page = page
		this.config = {
			// Default chart area - these coordinates may need adjustment based on Pocket Option's layout
			chartArea: { x: 300, y: 100, width: 800, height: 600 },
			// Default price area - typically in the top-right of the chart
			priceArea: { x: 900, y: 120, width: 200, height: 50 },
			debugMode: false,
			screenshotPath: './screenshots',
			...config
		}
	}

	async initialize(): Promise<void> {
		if (this.isInitialized) return

		try {
			// Initialize Tesseract worker
			this.worker = await Tesseract.createWorker('eng', 1, {
				logger: this.config.debugMode
					? (m: any) => {
							if (m && typeof m === 'object' && m.status) {
								console.log(`OCR: ${m.status} - ${Math.round((m.progress || 0) * 100)}%`)
							}
					  }
					: () => {} // Provide empty function instead of undefined
			})

			// Configure OCR for better number recognition
			await this.worker.setParameters({
				tessedit_char_whitelist: '0123456789.,',
				tessedit_pageseg_mode: Tesseract.PSM.SINGLE_LINE,
				preserve_interword_spaces: '0'
			})

			// Ensure screenshot directory exists
			if (this.config.screenshotPath && !fs.existsSync(this.config.screenshotPath)) {
				fs.mkdirSync(this.config.screenshotPath, { recursive: true })
			}

			this.isInitialized = true
			console.log('ScreenshotPriceExtractor initialized successfully')
		} catch (error) {
			console.error('Failed to initialize ScreenshotPriceExtractor:', error)
			throw error
		}
	}

	async extractPriceFromScreenshot(): Promise<number> {
		if (!this.isInitialized) {
			await this.initialize()
		}

		if (
			!this.config.priceArea ||
			typeof this.config.priceArea.x !== 'number' ||
			typeof this.config.priceArea.y !== 'number' ||
			typeof this.config.priceArea.width !== 'number' ||
			typeof this.config.priceArea.height !== 'number'
		) {
			console.warn('Screenshot price area is not configured. Please calibrate the screenshot extractor.')
			return 0
		}

		try {
			// Take screenshot of the entire page
			const fullScreenshot = await this.page.screenshot({ type: 'png' })

			// Extract price area from the screenshot
			const priceAreaImage = await this.extractPriceArea(fullScreenshot)

			// Preprocess the image for better OCR accuracy
			const processedImage = await this.preprocessImage(priceAreaImage)

			// Save debug images if enabled
			if (this.config.debugMode && this.config.screenshotPath) {
				const timestamp = Date.now()
				await fs.promises.writeFile(path.join(this.config.screenshotPath, `full_${timestamp}.png`), fullScreenshot)
				await fs.promises.writeFile(
					path.join(this.config.screenshotPath, `price_area_${timestamp}.png`),
					priceAreaImage
				)
				await fs.promises.writeFile(path.join(this.config.screenshotPath, `processed_${timestamp}.png`), processedImage)
			}

			// Perform OCR on the processed image
			const ocrResult = await this.performOCR(processedImage)

			// Extract and validate price from OCR result
			const price = this.extractPriceFromText(ocrResult.data.text)

			if (this.config.debugMode) {
				console.log('OCR Result:', ocrResult.data.text)
				console.log('Extracted Price:', price)
			}

			return price
		} catch (error) {
			console.error('Error extracting price from screenshot:', error)
			return 0
		}
	}

	private async extractPriceArea(fullScreenshot: Buffer): Promise<Buffer> {
		if (
			!this.config.priceArea ||
			typeof this.config.priceArea.x !== 'number' ||
			typeof this.config.priceArea.y !== 'number' ||
			typeof this.config.priceArea.width !== 'number' ||
			typeof this.config.priceArea.height !== 'number'
		) {
			throw new Error('Screenshot price area is not configured. Please calibrate the screenshot extractor.')
		}
		const priceArea = this.config.priceArea

		return await sharp(fullScreenshot)
			.extract({
				left: priceArea.x,
				top: priceArea.y,
				width: priceArea.width,
				height: priceArea.height
			})
			.png()
			.toBuffer()
	}

	private async preprocessImage(imageBuffer: Buffer): Promise<Buffer> {
		return await sharp(imageBuffer)
			// Resize for better OCR accuracy
			.resize({ width: 400, height: 100, fit: 'fill' })
			// Convert to grayscale
			.grayscale()
			// Increase brightness and saturation for better contrast
			.modulate({ brightness: 1.2, saturation: 1.5 })
			// Apply threshold to make text clearer
			.threshold(128)
			.png()
			.toBuffer()
	}

	private async performOCR(imageBuffer: Buffer): Promise<Tesseract.RecognizeResult> {
		if (!this.worker) {
			throw new Error('OCR worker not initialized')
		}

		return await this.worker.recognize(imageBuffer)
	}

	private extractPriceFromText(text: string): number {
		// Clean the text and extract numeric values
		const cleanText = text.replace(/\s+/g, '').replace(/[^\d.,]/g, '')

		// Look for price patterns (e.g., 1.23456, 123.45, etc.)
		const pricePatterns = [
			/(\d+\.\d{4,6})/, // Forex prices like 1.23456
			/(\d+\.\d{2,3})/, // Stock prices like 123.45
			/(\d+\.\d+)/, // Any decimal number
			/(\d+)/ // Whole numbers as fallback
		]

		for (const pattern of pricePatterns) {
			const match = cleanText.match(pattern)
			if (match) {
				const price = parseFloat(match[1])
				// Validate price range (adjust based on your trading assets)
				if (price > 0.001 && price < 1000000) {
					return price
				}
			}
		}

		return 0
	}

	/**
	 * Auto-detect and configure price extraction areas based on page elements
	 */
	async autoDetectPriceArea(): Promise<ChartArea | null> {
		try {
			this.log('Attempting to auto-detect price area from page elements...')

			// First try to find the chart canvas
			const chartCanvasInfo = await this.findChartCanvas()
			if (chartCanvasInfo) {
				this.log(`Found chart canvas: ${JSON.stringify(chartCanvasInfo)}`)

				// Price is typically displayed at the top-right of the chart
				// Calculate price area based on chart position
				const priceArea: ChartArea = {
					// Position at the right side of the chart, near top
					x: chartCanvasInfo.x + chartCanvasInfo.width - 200,
					y: chartCanvasInfo.y + 20,
					width: 180,
					height: 40
				}

				this.log(`Auto-detected price area: ${JSON.stringify(priceArea)}`)
				return priceArea
			}

			// If we can't find chart canvas, try to look for price elements
			const priceElements = await this.findPriceElements()
			if (priceElements.length > 0) {
				// Use the first price element as our target
				const priceElement = priceElements[0]
				this.log(`Using price element: ${JSON.stringify(priceElement)}`)

				const priceArea: ChartArea = {
					x: priceElement.x - 5,
					y: priceElement.y - 5,
					width: priceElement.width + 10,
					height: priceElement.height + 10
				}

				this.log(`Auto-detected price area from element: ${JSON.stringify(priceArea)}`)
				return priceArea
			}

			// Last resort: scan multiple regions of the screen
			return await this.scanScreenForPriceArea()
		} catch (error) {
			this.log(`Error in auto-detection: ${error}`, 'error')
			return null
		}
	}

	/**
	 * Find the main chart canvas on the page
	 */
	private async findChartCanvas(): Promise<ChartArea | null> {
		try {
			// Try to find chart canvases
			const canvasInfo = await this.page.evaluate(() => {
				// @ts-ignore - document is available in browser context
				const canvases = document.querySelectorAll('canvas.layer.plot, canvas[class*="chart"], canvas.trading-chart')

				if (canvases.length === 0) return null

				// Find the largest canvas (likely the main chart)
				let largestCanvas = null
				let largestArea = 0

				for (const canvas of canvases) {
					const rect = canvas.getBoundingClientRect()
					const area = rect.width * rect.height

					if (area > largestArea) {
						largestArea = area
						largestCanvas = {
							x: Math.round(rect.left),
							y: Math.round(rect.top),
							width: Math.round(rect.width),
							height: Math.round(rect.height)
						}
					}
				}

				return largestCanvas
			})

			return canvasInfo
		} catch (error) {
			this.log(`Error finding chart canvas: ${error}`, 'error')
			return null
		}
	}

	/**
	 * Find elements that likely display price information
	 */
	private async findPriceElements(): Promise<Array<ChartArea & { text: string }>> {
		try {
			return await this.page.evaluate(() => {
				// @ts-ignore - document is available in browser context
				const priceSelectors = [
					'.current-price',
					'.price-value',
					'.asset-price',
					'.live-rate',
					'.quote-value',
					'[data-price]',
					'[class*="price"][class*="current"]',
					'[class*="rate"][class*="live"]'
				]

				const elements = []

				for (const selector of priceSelectors) {
					// @ts-ignore - document is available in browser context
					const els = document.querySelectorAll(selector)
					for (const el of els) {
						const text = el.textContent?.trim() || ''
						// Only include elements with text that looks like a price
						if (/^\d+\.?\d*$/.test(text) || /\d+\.\d+/.test(text)) {
							const rect = el.getBoundingClientRect()
							elements.push({
								x: Math.round(rect.left),
								y: Math.round(rect.top),
								width: Math.round(rect.width),
								height: Math.round(rect.height),
								text: text
							})
						}
					}
				}

				return elements
			})
		} catch (error) {
			this.log(`Error finding price elements: ${error}`, 'error')
			return []
		}
	}

	/**
	 * Scan multiple regions of the screen for price information
	 */
	private async scanScreenForPriceArea(): Promise<ChartArea | null> {
		// Take a screenshot to analyze
		const screenshot = await this.page.screenshot({ type: 'png' })

		// Define grid of regions to test based on screen size
		const viewport = await this.page.evaluate(() => {
			return {
				// @ts-ignore - window is available in browser context
				width: window.innerWidth,
				// @ts-ignore - window is available in browser context
				height: window.innerHeight
			}
		})

		// Create grid of regions to test
		const regionWidth = 200
		const regionHeight = 50
		const regions: ChartArea[] = []

		// Focus on right side of screen where prices are typically shown
		const rightSide = Math.max(viewport.width * 0.6, viewport.width - 400)

		// Top right areas (most likely for price display)
		regions.push(
			{ x: rightSide, y: 20, width: regionWidth, height: regionHeight },
			{ x: rightSide, y: 80, width: regionWidth, height: regionHeight },
			{ x: rightSide, y: 140, width: regionWidth, height: regionHeight },
			{ x: viewport.width - regionWidth - 20, y: 20, width: regionWidth, height: regionHeight },
			{ x: viewport.width - regionWidth - 20, y: 80, width: regionWidth, height: regionHeight }
		)

		// Test each region
		for (const region of regions) {
			try {
				// Extract region
				const regionImage = await sharp(screenshot)
					.extract({
						left: region.x,
						top: region.y,
						width: region.width,
						height: region.height
					})
					.png()
					.toBuffer()

				// Preprocess for OCR
				const processedImage = await this.preprocessImage(regionImage)

				// Perform OCR
				const result = await this.performOCR(processedImage)
				const text = result.data.text.trim()

				// Check if we found price-like text
				if (/\d+\.\d+/.test(text)) {
					const price = this.extractPriceFromText(text)
					if (price > 0) {
						this.log(`Found price ${price} in region ${JSON.stringify(region)}`)
						return region
					}
				}
			} catch (error) {
				// Continue to next region
			}
		}

		// If all else fails, return a common default area
		return { x: viewport.width - 250, y: 50, width: 200, height: 50 }
	}

	/**
	 * Log message with optional level
	 */
	private log(message: string, level: 'info' | 'warn' | 'error' = 'info'): void {
		const prefix = 'ScreenshotExtractor:'
		if (level === 'error') {
			console.error(`${prefix} ${message}`)
		} else if (level === 'warn') {
			console.warn(`${prefix} ${message}`)
		} else {
			console.log(`${prefix} ${message}`)
		}
	}

	async findOptimalPriceArea(): Promise<ChartArea | null> {
		try {
			// First try auto-detection based on page elements
			const autoDetectedArea = await this.autoDetectPriceArea()
			if (autoDetectedArea) {
				// Validate auto-detected area
				const tempConfig = { ...this.config, priceArea: autoDetectedArea, debugMode: true }
				this.updateConfig(tempConfig)

				const priceTest = await this.extractPriceFromScreenshot()
				if (priceTest > 0) {
					this.log(`Auto-detected area validated with price: ${priceTest}`)
					return autoDetectedArea
				}
			}

			// Fall back to original approach of testing predefined areas
			this.log('Auto-detection failed, testing predefined areas')

			// Take a screenshot and analyze it to find the best price area
			const screenshot = await this.page.screenshot({ type: 'png' })

			// Dynamic test areas based on page size
			const viewport = await this.page.evaluate(() => {
				return {
					// @ts-ignore - window is available in browser context
					width: window.innerWidth,
					// @ts-ignore - window is available in browser context
					height: window.innerHeight
				}
			})

			// Create more test areas, focusing on regions where price is typically displayed
			const testAreas: ChartArea[] = [
				// Right side positions (common for price display)
				{ x: viewport.width - 300, y: 50, width: 200, height: 50 },
				{ x: viewport.width - 250, y: 100, width: 200, height: 50 },
				{ x: viewport.width - 350, y: 150, width: 250, height: 60 },
				// Top of chart positions
				{ x: Math.max(300, viewport.width / 2 - 100), y: 80, width: 200, height: 50 },
				{ x: Math.max(400, viewport.width / 2), y: 120, width: 200, height: 50 },
				// Original positions
				{ x: 900, y: 120, width: 200, height: 50 },
				{ x: 800, y: 100, width: 250, height: 60 },
				{ x: 1000, y: 150, width: 180, height: 40 },
				{ x: 850, y: 80, width: 220, height: 70 }
			]

			for (const area of testAreas) {
				try {
					const tempConfig = { ...this.config, priceArea: area, debugMode: false }
					const tempExtractor = new ScreenshotPriceExtractor(this.page, tempConfig)
					await tempExtractor.initialize()

					const price = await tempExtractor.extractPriceFromScreenshot()
					if (price > 0) {
						this.log(`Found optimal price area: ${JSON.stringify(area)} with price: ${price}`)
						await tempExtractor.cleanup()
						return area
					}

					await tempExtractor.cleanup()
				} catch (error) {
					// Continue to next area
				}
			}

			return null
		} catch (error) {
			console.error('Error finding optimal price area:', error)
			return null
		}
	}

	updateConfig(newConfig: Partial<PriceExtractionConfig>): void {
		this.config = { ...this.config, ...newConfig }
	}

	async cleanup(): Promise<void> {
		if (this.worker) {
			await this.worker.terminate()
			this.worker = null
		}
		this.isInitialized = false
	}
}
