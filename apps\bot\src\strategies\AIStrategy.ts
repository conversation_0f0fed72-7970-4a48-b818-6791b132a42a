import { PriceData } from '../../../../shared/types'
import { TradingStrategy, TradingDecision } from './TradingStrategy'
import { AIMarketAnalyzer, AIAnalysisResult, MarketContext } from '../services/AIMarketAnalyzer'

interface AIStrategyConfig {
	aiProvider: 'openai' | 'deepseek'
	apiKey: string
	minConfidence: number
	analysisInterval: number // Seconds between AI analyses
	fallbackStrategy: 'threshold' | 'momentum' | 'none'
	riskManagement: boolean
	maxDailyTrades: number
	enablePatternRecognition: boolean
}

export class AIStrategy extends TradingStrategy {
	private aiAnalyzer: AIMarketAnalyzer
	private lastAnalysisTime = 0
	private lastAIResult: AIAnalysisResult | null = null
	private dailyTradeCount = 0
	private lastResetDate = new Date().toDateString()
	private readonly COOLDOWN_PERIOD = 30000 // 30 seconds between trades

	getName(): string {
		return 'AI Strategy'
	}

	getDescription(): string {
		return 'Uses ChatGPT/DeepSeek AI for intelligent market analysis and trading decisions'
	}

	constructor(config: any) {
		super(config)

		const aiConfig: AIStrategyConfig = {
			aiProvider: config.aiProvider || 'openai',
			apiKey: config.apiKey || '',
			minConfidence: config.minConfidence || 0.7,
			analysisInterval: config.analysisInterval || 60, // 1 minute
			fallbackStrategy: config.fallbackStrategy || 'momentum',
			riskManagement: config.riskManagement !== false,
			maxDailyTrades: config.maxDailyTrades || 20,
			enablePatternRecognition: config.enablePatternRecognition !== false
		}

		if (!aiConfig.apiKey) {
			throw new Error('AI API key is required for AI strategy')
		}

		this.aiAnalyzer = new AIMarketAnalyzer(aiConfig.aiProvider, aiConfig.apiKey)
	}

	async evaluate(priceData: PriceData): Promise<TradingDecision> {
		// Add current price data to history
		this.addPriceData(priceData)

		const config = this.config as AIStrategyConfig

		// Reset daily trade count if new day
		this.resetDailyCountIfNeeded()

		// Check daily trade limit
		if (this.dailyTradeCount >= config.maxDailyTrades) {
			return {
				shouldTrade: false,
				reason: `Daily trade limit reached (${this.dailyTradeCount}/${config.maxDailyTrades})`
			}
		}

		// Check cooldown period
		const now = Date.now()
		if (now - this.lastTradeTime < this.COOLDOWN_PERIOD) {
			return {
				shouldTrade: false,
				reason: `Cooldown period active (${Math.round(
					(this.COOLDOWN_PERIOD - (now - this.lastTradeTime)) / 1000
				)}s remaining)`
			}
		}

		// Need sufficient data
		if (this.priceHistory.length < 20) {
			return {
				shouldTrade: false,
				reason: `Insufficient data for AI analysis (need 20+ points, have ${this.priceHistory.length})`
			}
		}

		// Check if we need new AI analysis
		const timeSinceLastAnalysis = (now - this.lastAnalysisTime) / 1000
		if (timeSinceLastAnalysis >= config.analysisInterval || !this.lastAIResult) {
			try {
				const marketContext = this.buildMarketContext()
				this.lastAIResult = await this.aiAnalyzer.analyzeMarket(marketContext)
				this.lastAnalysisTime = now

				console.log('AI Analysis Result:', this.lastAIResult)
			} catch (error) {
				console.error('AI analysis failed:', error)

				// Use fallback strategy
				if (config.fallbackStrategy !== 'none') {
					return this.fallbackAnalysis(priceData, config)
				} else {
					return {
						shouldTrade: false,
						reason: `AI analysis failed: ${error}`
					}
				}
			}
		}

		// Generate decision based on AI analysis
		return this.generateAIDecision(priceData, this.lastAIResult!, config)
	}

	private buildMarketContext(): MarketContext {
		const now = new Date()
		const timeOfDay = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`

		// Determine market session
		const hour = now.getUTCHours()
		let marketSession = 'off-hours'
		if (hour >= 13 && hour < 21) marketSession = 'us-session'
		else if (hour >= 7 && hour < 16) marketSession = 'eu-session'
		else if (hour >= 23 || hour < 8) marketSession = 'asia-session'

		// Calculate current trend
		const recentPrices = this.priceHistory.slice(-10).map(p => p.current)
		const firstPrice = recentPrices[0]
		const lastPrice = recentPrices[recentPrices.length - 1]
		const trendChange = ((lastPrice - firstPrice) / firstPrice) * 100

		let currentTrend = 'neutral'
		if (trendChange > 0.1) currentTrend = 'bullish'
		else if (trendChange < -0.1) currentTrend = 'bearish'

		// Calculate volatility
		const changes = this.priceHistory.slice(-20).map(p => p.changePercent)
		const avgChange = changes.reduce((sum, change) => sum + Math.abs(change), 0) / changes.length
		const volatility = avgChange / 100

		return {
			priceHistory: this.priceHistory.slice(-50), // Last 50 data points
			currentTrend,
			volatility,
			timeOfDay,
			marketSession
		}
	}

	private generateAIDecision(
		priceData: PriceData,
		aiResult: AIAnalysisResult,
		config: AIStrategyConfig
	): TradingDecision {
		// Check AI confidence threshold
		if (aiResult.confidence < config.minConfidence) {
			return {
				shouldTrade: false,
				reason: `AI confidence ${(aiResult.confidence * 100).toFixed(1)}% below threshold ${(
					config.minConfidence * 100
				).toFixed(1)}%`
			}
		}

		// Don't trade on hold signals
		if (aiResult.direction === 'hold') {
			return {
				shouldTrade: false,
				reason: `AI recommends holding: ${aiResult.reasoning}`
			}
		}

		// Apply risk management if enabled
		if (config.riskManagement) {
			const riskCheck = this.performRiskCheck(priceData, aiResult)
			if (!riskCheck.passed) {
				return {
					shouldTrade: false,
					reason: `Risk management: ${riskCheck.reason}`
				}
			}
		}

		// Apply pattern recognition if enabled
		if (config.enablePatternRecognition) {
			const patterns = this.aiAnalyzer.analyzePatterns(this.priceHistory)
			if (patterns.length > 0) {
				console.log('Detected patterns:', patterns)

				// Adjust confidence based on patterns
				let patternConfidence = aiResult.confidence

				if (patterns.includes('double_top') && aiResult.direction === 'low') {
					patternConfidence *= 1.2
				} else if (patterns.includes('double_bottom') && aiResult.direction === 'high') {
					patternConfidence *= 1.2
				} else if (patterns.includes('uptrend') && aiResult.direction === 'high') {
					patternConfidence *= 1.1
				} else if (patterns.includes('downtrend') && aiResult.direction === 'low') {
					patternConfidence *= 1.1
				}

				aiResult.confidence = Math.min(1, patternConfidence)
			}
		}

		// Final confidence check after adjustments
		if (aiResult.confidence < config.minConfidence) {
			return {
				shouldTrade: false,
				reason: `Adjusted AI confidence ${(aiResult.confidence * 100).toFixed(
					1
				)}% below threshold after pattern analysis`
			}
		}

		// Increment daily trade count
		this.dailyTradeCount++

		return {
			shouldTrade: true,
			direction: aiResult.direction,
			confidence: aiResult.confidence,
			reason: `AI Analysis: ${aiResult.reasoning}`,
			metadata: {
				strategy: 'ai',
				aiProvider: config.aiProvider,
				timeframe: aiResult.timeframe,
				riskLevel: aiResult.riskLevel,
				suggestedAmount: aiResult.suggestedAmount,
				analysisTime: this.lastAnalysisTime
			}
		}
	}

	private performRiskCheck(priceData: PriceData, aiResult: AIAnalysisResult): { passed: boolean; reason?: string } {
		// Check for high volatility
		const recentChanges = this.priceHistory.slice(-10).map(p => Math.abs(p.changePercent))
		const avgVolatility = recentChanges.reduce((sum, change) => sum + change, 0) / recentChanges.length

		if (avgVolatility > 2 && aiResult.riskLevel === 'high') {
			return {
				passed: false,
				reason: 'High volatility combined with high-risk AI signal'
			}
		}

		// Check for rapid price movements
		const lastChange = Math.abs(priceData.changePercent)
		if (lastChange > 1) {
			return {
				passed: false,
				reason: `Rapid price movement detected: ${lastChange.toFixed(2)}%`
			}
		}

		// Check for conflicting signals
		const recentTrend = this.calculateRecentTrend(5)
		if (recentTrend.direction !== 'neutral') {
			const aiDirection = aiResult.direction === 'high' ? 'up' : 'low'
			if (recentTrend.direction !== aiDirection && recentTrend.strength > 0.5) {
				return {
					passed: false,
					reason: 'AI signal conflicts with strong recent trend'
				}
			}
		}

		return { passed: true }
	}

	private calculateRecentTrend(periods: number): { direction: 'up' | 'down' | 'neutral'; strength: number } {
		if (this.priceHistory.length < periods) {
			return { direction: 'neutral', strength: 0 }
		}

		const recentPrices = this.priceHistory.slice(-periods).map(p => p.current)
		const firstPrice = recentPrices[0]
		const lastPrice = recentPrices[recentPrices.length - 1]

		const change = (lastPrice - firstPrice) / firstPrice
		const direction = change > 0.001 ? 'up' : change < -0.001 ? 'down' : 'neutral'
		const strength = Math.min(1, Math.abs(change) * 100)

		return { direction, strength }
	}

	private fallbackAnalysis(priceData: PriceData, config: AIStrategyConfig): TradingDecision {
		if (config.fallbackStrategy === 'none') {
			return {
				shouldTrade: false,
				reason: 'AI analysis failed and no fallback strategy configured'
			}
		}

		// Simple momentum-based fallback
		if (config.fallbackStrategy === 'momentum') {
			const momentum = this.calculateRecentTrend(5)

			if (momentum.strength > 0.3 && momentum.direction !== 'neutral') {
				return {
					shouldTrade: true,
					direction: momentum.direction === 'up' ? 'high' : 'low',
					confidence: Math.min(0.6, momentum.strength),
					reason: `Fallback momentum strategy: ${momentum.direction} trend with ${momentum.strength.toFixed(
						2
					)} strength`
				}
			}
		}

		// Simple threshold-based fallback
		if (config.fallbackStrategy === 'threshold') {
			const changePercent = Math.abs(priceData.changePercent)
			if (changePercent >= 0.02) {
				return {
					shouldTrade: true,
					direction: priceData.trend === 'up' ? 'high' : 'low',
					confidence: Math.min(0.6, changePercent * 10),
					reason: `Fallback threshold strategy: ${changePercent.toFixed(2)}% change detected`
				}
			}
		}

		return {
			shouldTrade: false,
			reason: 'Fallback analysis did not generate trading signal'
		}
	}

	private resetDailyCountIfNeeded(): void {
		const today = new Date().toDateString()
		if (today !== this.lastResetDate) {
			this.dailyTradeCount = 0
			this.lastResetDate = today
		}
	}

	// Get AI analysis result for UI display
	getLastAIResult(): AIAnalysisResult | null {
		return this.lastAIResult
	}

	// Get strategy status
	getStatus(): any {
		return {
			dailyTradeCount: this.dailyTradeCount,
			maxDailyTrades: (this.config as AIStrategyConfig).maxDailyTrades,
			lastAnalysisTime: this.lastAnalysisTime,
			lastAIResult: this.lastAIResult,
			cooldownRemaining: Math.max(0, this.COOLDOWN_PERIOD - (Date.now() - this.lastTradeTime))
		}
	}

	private lastTradeTime = 0
}
