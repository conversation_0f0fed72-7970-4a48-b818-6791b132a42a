import { PriceData } from '../../../../shared/types'
import { TradingStrategy, TradingDecision } from './TradingStrategy'

interface MACDAnalysis {
	macdLine: number
	signalLine: number
	histogram: number
	previousMACDLine: number
	previousSignalLine: number
	previousHistogram: number
	crossover: 'bullish' | 'bearish' | 'none'
	crossoverStrength: number
	trend: 'bullish' | 'bearish' | 'neutral'
	momentum: 'increasing' | 'decreasing' | 'stable'
	divergence: 'bullish' | 'bearish' | 'none'
}

export class MACDStrategy extends TradingStrategy {
	private lastTradeTime: number = 0
	private readonly COOLDOWN_PERIOD = 60000 // 60 seconds cooldown
	private macdHistory: Array<{ macd: number; signal: number; histogram: number }> = []
	private emaFastHistory: number[] = []
	private emaSlowHistory: number[] = []
	private emaSignalHistory: number[] = []

	getName(): string {
		return 'Enhanced MACD Strategy'
	}

	getDescription(): string {
		return 'Advanced MACD strategy with proper EMA calculation, signal line crossovers, and divergence detection'
	}

	async evaluate(priceData: PriceData): Promise<TradingDecision> {
		// Add current price data to history
		this.addPriceData(priceData)

		// Configuration with enhanced defaults
		const fastPeriod = this.config.fastPeriod || 12
		const slowPeriod = this.config.slowPeriod || 26
		const signalPeriod = this.config.signalPeriod || 9
		const minConfidence = this.config.minConfidence || 0.6
		const enableDivergence = this.config.enableDivergence !== false // Default true
		const histogramFilter = this.config.histogramFilter !== false // Default true

		// Check cooldown period
		const now = Date.now()
		if (now - this.lastTradeTime < this.COOLDOWN_PERIOD) {
			return {
				shouldTrade: false,
				reason: `Cooldown period active (${Math.round(
					(this.COOLDOWN_PERIOD - (now - this.lastTradeTime)) / 1000
				)}s remaining)`
			}
		}

		// Need enough data for MACD calculation
		if (this.priceHistory.length < slowPeriod + signalPeriod) {
			return {
				shouldTrade: false,
				reason: `Insufficient data for MACD calculation (need ${slowPeriod + signalPeriod} data points)`
			}
		}

		// Calculate MACD components
		const macdData = this.calculateMACD(fastPeriod, slowPeriod, signalPeriod)
		if (!macdData) {
			return {
				shouldTrade: false,
				reason: 'Unable to calculate MACD components'
			}
		}

		// Update MACD history
		this.macdHistory.push({
			macd: macdData.macdLine,
			signal: macdData.signalLine,
			histogram: macdData.histogram
		})
		if (this.macdHistory.length > 50) {
			this.macdHistory = this.macdHistory.slice(-50)
		}

		// Perform comprehensive MACD analysis
		const analysis = this.analyzeMACDSignals(macdData, priceData)

		// Generate trading decision
		const decision = this.generateTradingDecision(analysis, priceData, minConfidence, enableDivergence, histogramFilter)

		// Update last trade time if we're making a trade
		if (decision.shouldTrade) {
			this.lastTradeTime = now
		}

		return decision
	}

	/**
	 * Calculate MACD using proper EMA method
	 */
	private calculateMACD(fastPeriod: number, slowPeriod: number, signalPeriod: number) {
		const prices = this.priceHistory.map(p => p.current)

		// Calculate EMAs
		const fastEMA = this.calculateEMA(prices, fastPeriod)
		const slowEMA = this.calculateEMA(prices, slowPeriod)

		if (fastEMA === null || slowEMA === null) return null

		// MACD Line = Fast EMA - Slow EMA
		const macdLine = fastEMA - slowEMA

		// Calculate Signal Line (EMA of MACD Line)
		this.updateMACDLineHistory(macdLine)
		const signalLine = this.calculateSignalLine(signalPeriod)

		if (signalLine === null) return null

		// Histogram = MACD Line - Signal Line
		const histogram = macdLine - signalLine

		return {
			macdLine,
			signalLine,
			histogram
		}
	}

	/**
	 * Calculate Exponential Moving Average
	 */
	private calculateEMA(prices: number[], period: number): number | null {
		if (prices.length < period) return null

		const multiplier = 2 / (period + 1)
		let ema = prices.slice(0, period).reduce((sum, price) => sum + price, 0) / period

		for (let i = period; i < prices.length; i++) {
			ema = prices[i] * multiplier + ema * (1 - multiplier)
		}

		return ema
	}

	/**
	 * Update MACD line history for signal line calculation
	 */
	private updateMACDLineHistory(macdLine: number): void {
		// Keep a separate history for MACD line values to calculate signal line
		if (!this.emaSignalHistory) {
			this.emaSignalHistory = []
		}
		this.emaSignalHistory.push(macdLine)
		if (this.emaSignalHistory.length > 100) {
			this.emaSignalHistory = this.emaSignalHistory.slice(-100)
		}
	}

	/**
	 * Calculate Signal Line (EMA of MACD Line)
	 */
	private calculateSignalLine(signalPeriod: number): number | null {
		if (this.emaSignalHistory.length < signalPeriod) return null

		const multiplier = 2 / (signalPeriod + 1)
		const initialValues = this.emaSignalHistory.slice(0, signalPeriod)
		let ema = initialValues.reduce((sum, val) => sum + val, 0) / signalPeriod

		for (let i = signalPeriod; i < this.emaSignalHistory.length; i++) {
			ema = this.emaSignalHistory[i] * multiplier + ema * (1 - multiplier)
		}

		return ema
	}

	/**
	 * Analyze MACD signals and patterns
	 */
	private analyzeMACDSignals(
		macdData: { macdLine: number; signalLine: number; histogram: number },
		priceData: PriceData
	): MACDAnalysis {
		const { macdLine, signalLine, histogram } = macdData

		// Get previous values for crossover detection
		const previousData = this.macdHistory.length > 0 ? this.macdHistory[this.macdHistory.length - 1] : null
		const previousMACDLine = previousData?.macd || macdLine
		const previousSignalLine = previousData?.signal || signalLine
		const previousHistogram = previousData?.histogram || histogram

		// Detect crossovers
		let crossover: 'bullish' | 'bearish' | 'none' = 'none'
		let crossoverStrength = 0

		if (previousData) {
			// Bullish crossover: MACD crosses above signal line
			if (previousMACDLine <= previousSignalLine && macdLine > signalLine) {
				crossover = 'bullish'
				crossoverStrength = Math.min(Math.abs(macdLine - signalLine) / Math.abs(signalLine), 0.1) * 10
			}
			// Bearish crossover: MACD crosses below signal line
			else if (previousMACDLine >= previousSignalLine && macdLine < signalLine) {
				crossover = 'bearish'
				crossoverStrength = Math.min(Math.abs(signalLine - macdLine) / Math.abs(signalLine), 0.1) * 10
			}
		}

		// Determine trend
		let trend: 'bullish' | 'bearish' | 'neutral' = 'neutral'
		if (macdLine > signalLine && macdLine > 0) trend = 'bullish'
		else if (macdLine < signalLine && macdLine < 0) trend = 'bearish'

		// Analyze momentum
		let momentum: 'increasing' | 'decreasing' | 'stable' = 'stable'
		if (previousData) {
			const histogramChange = histogram - previousHistogram
			if (Math.abs(histogramChange) > 0.001) {
				momentum = histogramChange > 0 ? 'increasing' : 'decreasing'
			}
		}

		// Simple divergence detection
		let divergence: 'bullish' | 'bearish' | 'none' = 'none'
		if (this.macdHistory.length >= 5) {
			divergence = this.detectMACDDivergence(priceData)
		}

		return {
			macdLine,
			signalLine,
			histogram,
			previousMACDLine,
			previousSignalLine,
			previousHistogram,
			crossover,
			crossoverStrength,
			trend,
			momentum,
			divergence
		}
	}

	/**
	 * Detect MACD divergence with price
	 */
	private detectMACDDivergence(priceData: PriceData): 'bullish' | 'bearish' | 'none' {
		if (this.macdHistory.length < 10 || this.priceHistory.length < 10) return 'none'

		const recentMACD = this.macdHistory.slice(-10).map(m => m.macd)
		const recentPrices = this.priceHistory.slice(-10).map(p => p.current)

		// Simple divergence check: compare recent highs/lows
		const macdHigh = Math.max(...recentMACD)
		const macdLow = Math.min(...recentMACD)
		const priceHigh = Math.max(...recentPrices)
		const priceLow = Math.min(...recentPrices)

		const currentMACD = recentMACD[recentMACD.length - 1]
		const currentPrice = priceData.current

		// Bullish divergence: price makes lower low, MACD makes higher low
		if (currentPrice <= priceLow * 1.01 && currentMACD >= macdLow * 1.1) {
			return 'bullish'
		}

		// Bearish divergence: price makes higher high, MACD makes lower high
		if (currentPrice >= priceHigh * 0.99 && currentMACD <= macdHigh * 0.9) {
			return 'bearish'
		}

		return 'none'
	}

	/**
	 * Generate comprehensive trading decision
	 */
	private generateTradingDecision(
		analysis: MACDAnalysis,
		priceData: PriceData,
		minConfidence: number,
		enableDivergence: boolean,
		histogramFilter: boolean
	): TradingDecision {
		let shouldTrade = false
		let direction: 'high' | 'low' | undefined
		let confidence = 0
		let reason = ''

		// Primary signal: MACD crossovers
		if (analysis.crossover === 'bullish') {
			shouldTrade = true
			direction = 'high'
			confidence = 0.7 + analysis.crossoverStrength * 0.3
			reason = `Bullish MACD crossover (strength: ${analysis.crossoverStrength.toFixed(2)})`
		} else if (analysis.crossover === 'bearish') {
			shouldTrade = true
			direction = 'low'
			confidence = 0.7 + analysis.crossoverStrength * 0.3
			reason = `Bearish MACD crossover (strength: ${analysis.crossoverStrength.toFixed(2)})`
		}

		// Secondary signals: Zero line crossovers
		if (!shouldTrade) {
			if (analysis.macdLine > 0 && analysis.previousMACDLine <= 0) {
				shouldTrade = true
				direction = 'high'
				confidence = 0.6
				reason = `MACD crosses above zero line`
			} else if (analysis.macdLine < 0 && analysis.previousMACDLine >= 0) {
				shouldTrade = true
				direction = 'low'
				confidence = 0.6
				reason = `MACD crosses below zero line`
			}
		}

		// Apply histogram filter
		if (histogramFilter && shouldTrade) {
			if (direction === 'high' && analysis.histogram < 0) {
				confidence -= 0.2
				reason += ` (negative histogram reduces confidence)`
			} else if (direction === 'low' && analysis.histogram > 0) {
				confidence -= 0.2
				reason += ` (positive histogram reduces confidence)`
			}
		}

		// Apply momentum modifier
		if (shouldTrade) {
			if (
				(direction === 'high' && analysis.momentum === 'increasing') ||
				(direction === 'low' && analysis.momentum === 'decreasing')
			) {
				confidence += 0.1
				reason += ` with ${analysis.momentum} momentum`
			} else if (
				(direction === 'high' && analysis.momentum === 'decreasing') ||
				(direction === 'low' && analysis.momentum === 'increasing')
			) {
				confidence -= 0.1
				reason += ` but ${analysis.momentum} momentum`
			}
		}

		// Apply divergence modifier
		if (enableDivergence && shouldTrade) {
			if (
				(direction === 'high' && analysis.divergence === 'bullish') ||
				(direction === 'low' && analysis.divergence === 'bearish')
			) {
				confidence += 0.15
				reason += ` with ${analysis.divergence} divergence`
			} else if (
				(direction === 'high' && analysis.divergence === 'bearish') ||
				(direction === 'low' && analysis.divergence === 'bullish')
			) {
				confidence -= 0.15
				reason += ` but conflicting ${analysis.divergence} divergence`
			}
		}

		// Ensure confidence is within bounds
		confidence = Math.max(0, Math.min(1, confidence))

		// Final decision
		if (!shouldTrade || confidence < minConfidence) {
			return {
				shouldTrade: false,
				reason: shouldTrade
					? `${reason} - confidence ${confidence.toFixed(2)} below minimum ${minConfidence}`
					: `No clear MACD signal (MACD: ${analysis.macdLine.toFixed(4)}, Signal: ${analysis.signalLine.toFixed(4)})`
			}
		}

		return {
			shouldTrade: true,
			direction,
			confidence,
			reason: `${reason} - confidence: ${confidence.toFixed(2)}`
		}
	}
}
