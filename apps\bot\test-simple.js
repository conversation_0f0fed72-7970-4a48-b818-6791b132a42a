/**
 * Simple test to verify the logger fix
 */

const { ScreenshotPriceExtractor } = require('./dist/apps/bot/src/ScreenshotPriceExtractor.js')

async function testLoggerFix() {
    console.log('Testing logger fix...')
    
    try {
        // Create a mock page object
        const mockPage = {
            screenshot: async () => Buffer.from('mock'),
            waitForTimeout: async () => {},
            url: () => 'https://example.com'
        }
        
        // Test creating extractor with debug mode
        const extractor = new ScreenshotPriceExtractor(mockPage, {
            debugMode: true,
            screenshotPath: './test-screenshots'
        })
        
        console.log('✓ ScreenshotPriceExtractor created successfully')
        
        // Test initialization (this is where the logger error occurred)
        await extractor.initialize()
        
        console.log('✓ Initialization completed without logger error')
        
        // Cleanup
        await extractor.cleanup()
        
        console.log('✓ Test completed successfully - logger fix works!')
        
    } catch (error) {
        console.error('✗ Test failed:', error.message)
        process.exit(1)
    }
}

testLoggerFix().catch(console.error)
