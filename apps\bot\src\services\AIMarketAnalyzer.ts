import { PriceData } from '../../../../shared/types'

export interface AIAnalysisResult {
	direction: 'high' | 'low' | 'hold'
	confidence: number // 0-1
	reasoning: string
	timeframe: string
	riskLevel: 'low' | 'medium' | 'high'
	suggestedAmount?: number
}

export interface MarketContext {
	priceHistory: PriceData[]
	currentTrend: string
	volatility: number
	timeOfDay: string
	marketSession: string
}

export class AIMarketAnalyzer {
	private apiKey: string
	private provider: 'openai' | 'deepseek'
	private baseUrl: string

	constructor(provider: 'openai' | 'deepseek' = 'openai', apiKey: string) {
		this.provider = provider
		this.apiKey = apiKey
		this.baseUrl =
			provider === 'openai'
				? 'https://api.openai.com/v1/chat/completions'
				: 'https://api.deepseek.com/v1/chat/completions'
	}

	async analyzeMarket(context: MarketContext): Promise<AIAnalysisResult> {
		try {
			const prompt = this.buildAnalysisPrompt(context)
			const response = await this.callAI(prompt)
			return this.parseAIResponse(response)
		} catch (error) {
			console.error('AI analysis failed:', error)
			// Fallback to basic analysis
			return this.fallbackAnalysis(context)
		}
	}

	private buildAnalysisPrompt(context: MarketContext): string {
		const { priceHistory, currentTrend, volatility, timeOfDay, marketSession } = context

		const recentPrices = priceHistory.slice(-20).map(p => ({
			price: p.current,
			timestamp: new Date(p.timestamp).toISOString(),
			change: p.changePercent
		}))

		return `You are an expert binary options trading analyst. Analyze the following market data and provide a trading recommendation.

MARKET DATA:
- Current Trend: ${currentTrend}
- Volatility: ${volatility.toFixed(4)}
- Time of Day: ${timeOfDay}
- Market Session: ${marketSession}
- Recent Price History (last 20 points): ${JSON.stringify(recentPrices, null, 2)}

ANALYSIS REQUIREMENTS:
1. Determine if the market is trending or ranging
2. Assess momentum strength and direction
3. Consider time-based factors (market hours, volatility patterns)
4. Evaluate risk vs reward potential
5. Provide a clear HIGH/LOW/HOLD recommendation

RESPONSE FORMAT (JSON only):
{
  "direction": "high|low|hold",
  "confidence": 0.0-1.0,
  "reasoning": "detailed explanation of analysis",
  "timeframe": "1m|5m|15m",
  "riskLevel": "low|medium|high",
  "suggestedAmount": optional_percentage_of_balance
}

Focus on:
- Price action patterns
- Support/resistance levels
- Momentum indicators
- Market timing
- Risk management

Provide only the JSON response, no additional text.`
	}

	private async callAI(prompt: string): Promise<string> {
		const requestBody = {
			model: this.provider === 'openai' ? 'gpt-4' : 'deepseek-chat',
			messages: [
				{
					role: 'system',
					content: 'You are a professional trading analyst. Respond only with valid JSON.'
				},
				{
					role: 'user',
					content: prompt
				}
			],
			temperature: 0.3,
			max_tokens: 500
		}

		const response = await fetch(this.baseUrl, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${this.apiKey}`
			},
			body: JSON.stringify(requestBody)
		})

		if (!response.ok) {
			throw new Error(`AI API error: ${response.status} ${response.statusText}`)
		}

		const data = (await response.json()) as any
		return data.choices[0]?.message?.content || ''
	}

	private parseAIResponse(response: string): AIAnalysisResult {
		try {
			// Clean the response to extract JSON
			const jsonMatch = response.match(/\{[\s\S]*\}/)
			if (!jsonMatch) {
				throw new Error('No JSON found in response')
			}

			const parsed = JSON.parse(jsonMatch[0])

			// Validate required fields
			if (!parsed.direction || !parsed.confidence || !parsed.reasoning) {
				throw new Error('Missing required fields in AI response')
			}

			return {
				direction: parsed.direction,
				confidence: Math.max(0, Math.min(1, parsed.confidence)),
				reasoning: parsed.reasoning,
				timeframe: parsed.timeframe || '1m',
				riskLevel: parsed.riskLevel || 'medium',
				suggestedAmount: parsed.suggestedAmount
			}
		} catch (error) {
			console.error('Failed to parse AI response:', error)
			throw new Error('Invalid AI response format')
		}
	}

	private fallbackAnalysis(context: MarketContext): AIAnalysisResult {
		const { priceHistory, volatility } = context

		if (priceHistory.length < 5) {
			return {
				direction: 'hold',
				confidence: 0.1,
				reasoning: 'Insufficient data for analysis',
				timeframe: '1m',
				riskLevel: 'high'
			}
		}

		const recentChanges = priceHistory.slice(-5).map(p => p.changePercent)
		const avgChange = recentChanges.reduce((sum, change) => sum + change, 0) / recentChanges.length

		// Simple momentum-based fallback
		if (Math.abs(avgChange) < 0.01) {
			return {
				direction: 'hold',
				confidence: 0.3,
				reasoning: 'Low momentum detected, waiting for clearer signal',
				timeframe: '1m',
				riskLevel: 'medium'
			}
		}

		const direction = avgChange > 0 ? 'high' : 'low'
		const confidence = Math.min(0.7, Math.abs(avgChange) * 10)

		return {
			direction,
			confidence,
			reasoning: `Fallback analysis: ${direction} momentum with ${avgChange.toFixed(3)}% average change`,
			timeframe: '1m',
			riskLevel: volatility > 0.02 ? 'high' : 'medium'
		}
	}

	// Pattern recognition methods
	analyzePatterns(priceHistory: PriceData[]): string[] {
		const patterns: string[] = []

		if (priceHistory.length < 10) return patterns

		// Detect common patterns
		const prices = priceHistory.slice(-10).map(p => p.current)

		// Double top/bottom
		if (this.isDoubleTop(prices)) patterns.push('double_top')
		if (this.isDoubleBottom(prices)) patterns.push('double_bottom')

		// Trend patterns
		if (this.isUptrend(prices)) patterns.push('uptrend')
		if (this.isDowntrend(prices)) patterns.push('downtrend')

		// Consolidation
		if (this.isConsolidating(prices)) patterns.push('consolidation')

		return patterns
	}

	private isDoubleTop(prices: number[]): boolean {
		if (prices.length < 5) return false
		const max1 = Math.max(...prices.slice(0, 3))
		const max2 = Math.max(...prices.slice(-3))
		const middle = Math.min(...prices.slice(2, -2))
		return Math.abs(max1 - max2) / max1 < 0.01 && middle < max1 * 0.98
	}

	private isDoubleBottom(prices: number[]): boolean {
		if (prices.length < 5) return false
		const min1 = Math.min(...prices.slice(0, 3))
		const min2 = Math.min(...prices.slice(-3))
		const middle = Math.max(...prices.slice(2, -2))
		return Math.abs(min1 - min2) / min1 < 0.01 && middle > min1 * 1.02
	}

	private isUptrend(prices: number[]): boolean {
		const highs = []
		const lows = []

		for (let i = 1; i < prices.length - 1; i++) {
			if (prices[i] > prices[i - 1] && prices[i] > prices[i + 1]) {
				highs.push(prices[i])
			}
			if (prices[i] < prices[i - 1] && prices[i] < prices[i + 1]) {
				lows.push(prices[i])
			}
		}

		return (
			highs.length >= 2 && lows.length >= 2 && highs[highs.length - 1] > highs[0] && lows[lows.length - 1] > lows[0]
		)
	}

	private isDowntrend(prices: number[]): boolean {
		const highs = []
		const lows = []

		for (let i = 1; i < prices.length - 1; i++) {
			if (prices[i] > prices[i - 1] && prices[i] > prices[i + 1]) {
				highs.push(prices[i])
			}
			if (prices[i] < prices[i - 1] && prices[i] < prices[i + 1]) {
				lows.push(prices[i])
			}
		}

		return (
			highs.length >= 2 && lows.length >= 2 && highs[highs.length - 1] < highs[0] && lows[lows.length - 1] < lows[0]
		)
	}

	private isConsolidating(prices: number[]): boolean {
		const max = Math.max(...prices)
		const min = Math.min(...prices)
		const range = (max - min) / min
		return range < 0.02 // Less than 2% range
	}
}
