import { PriceData } from '../../../../shared/types'
import { TradingStrategy, TradingDecision } from './TradingStrategy'

interface BollingerAnalysis {
	upperBand: number
	middleBand: number
	lowerBand: number
	currentPrice: number
	bandWidth: number
	percentB: number // Position within bands (0-1)
	squeeze: boolean
	volatility: 'high' | 'normal' | 'low'
	pricePosition: 'above_upper' | 'upper_zone' | 'middle_zone' | 'lower_zone' | 'below_lower'
	momentum: 'bullish' | 'bearish' | 'neutral'
	breakoutDirection: 'up' | 'down' | 'none'
}

export class BollingerBandsStrategy extends TradingStrategy {
	private lastTradeTime: number = 0
	private readonly COOLDOWN_PERIOD = 45000 // 45 seconds cooldown
	private bandWidthHistory: number[] = []

	getName(): string {
		return 'Enhanced Bollinger Bands Strategy'
	}

	getDescription(): string {
		return 'Advanced Bollinger Bands strategy with squeeze detection, volatility analysis, and adaptive breakout/reversion modes'
	}

	async evaluate(priceData: PriceData): Promise<TradingDecision> {
		// Add current price data to history
		this.addPriceData(priceData)

		// Configuration with enhanced defaults
		const period = this.config.period || 20
		const standardDeviations = this.config.standardDeviations || 2
		const minConfidence = this.config.minConfidence || 0.6
		const squeezeDetection = this.config.squeezeDetection !== false // Default true
		const volatilityBreakout = this.config.volatilityBreakout !== false // Default true
		const meanReversion = this.config.meanReversion !== false // Default true

		// Check cooldown period
		const now = Date.now()
		if (now - this.lastTradeTime < this.COOLDOWN_PERIOD) {
			return {
				shouldTrade: false,
				reason: `Cooldown period active (${Math.round(
					(this.COOLDOWN_PERIOD - (now - this.lastTradeTime)) / 1000
				)}s remaining)`
			}
		}

		if (this.priceHistory.length < period) {
			return {
				shouldTrade: false,
				reason: `Insufficient data for Bollinger Bands calculation (need ${period} data points)`
			}
		}

		// Calculate Bollinger Bands
		const middleBand = this.getSimpleMovingAverage(period)
		if (middleBand === null) {
			return {
				shouldTrade: false,
				reason: 'Unable to calculate middle band (SMA)'
			}
		}

		const prices = this.priceHistory.slice(-period).map(p => p.current)
		const stdDev = this.getStandardDeviation(prices)

		const upperBand = middleBand + standardDeviations * stdDev
		const lowerBand = middleBand - standardDeviations * stdDev

		// Perform comprehensive Bollinger Bands analysis
		const analysis = this.analyzeBollingerBands(priceData, upperBand, middleBand, lowerBand, stdDev)

		// Generate trading decision
		const decision = this.generateTradingDecision(
			analysis,
			minConfidence,
			squeezeDetection,
			volatilityBreakout,
			meanReversion
		)

		// Update last trade time if we're making a trade
		if (decision.shouldTrade) {
			this.lastTradeTime = now
		}

		return decision
	}

	/**
	 * Analyze Bollinger Bands patterns and market conditions
	 */
	private analyzeBollingerBands(
		priceData: PriceData,
		upperBand: number,
		middleBand: number,
		lowerBand: number,
		stdDev: number
	): BollingerAnalysis {
		const currentPrice = priceData.current

		// Calculate band width (volatility measure) with zero check
		const bandWidth = Math.abs(middleBand) > 0.00001 ? (upperBand - lowerBand) / middleBand : 0
		this.bandWidthHistory.push(bandWidth)
		if (this.bandWidthHistory.length > 20) {
			this.bandWidthHistory = this.bandWidthHistory.slice(-20)
		}

		// Calculate %B (position within bands) with zero check
		const bandDifference = upperBand - lowerBand
		const percentB = Math.abs(bandDifference) > 0.00001 ? (currentPrice - lowerBand) / bandDifference : 0.5 // Default to middle position when bands are identical

		// Detect squeeze (low volatility)
		const avgBandWidth =
			this.bandWidthHistory.length > 10
				? this.bandWidthHistory.slice(-10).reduce((sum, bw) => sum + bw, 0) / 10
				: bandWidth
		const squeeze = bandWidth < avgBandWidth * 0.8

		// Determine volatility level
		let volatility: 'high' | 'normal' | 'low' = 'normal'
		if (bandWidth > avgBandWidth * 1.5) volatility = 'high'
		else if (bandWidth < avgBandWidth * 0.7) volatility = 'low'

		// Determine price position
		let pricePosition: BollingerAnalysis['pricePosition'] = 'middle_zone'
		if (currentPrice > upperBand) pricePosition = 'above_upper'
		else if (currentPrice > middleBand + stdDev * 0.5) pricePosition = 'upper_zone'
		else if (currentPrice < lowerBand) pricePosition = 'below_lower'
		else if (currentPrice < middleBand - stdDev * 0.5) pricePosition = 'lower_zone'

		// Analyze momentum
		let momentum: 'bullish' | 'bearish' | 'neutral' = 'neutral'
		if (this.priceHistory.length >= 3) {
			const recentPrices = this.priceHistory.slice(-3).map(p => p.current)
			const priceChange = recentPrices[2] - recentPrices[0]
			// Add safety check for division by zero
			const priceChangePercent = Math.abs(recentPrices[0]) > 0.00001 ? Math.abs(priceChange) / recentPrices[0] : 0 // Default to no change if base price is zero

			if (priceChangePercent > 0.005) {
				// 0.5% threshold
				momentum = priceChange > 0 ? 'bullish' : 'bearish'
			}
		}

		// Detect breakout direction
		let breakoutDirection: 'up' | 'down' | 'none' = 'none'
		if (currentPrice > upperBand && priceData.trend === 'up') breakoutDirection = 'up'
		else if (currentPrice < lowerBand && priceData.trend === 'down') breakoutDirection = 'down'

		return {
			upperBand,
			middleBand,
			lowerBand,
			currentPrice,
			bandWidth,
			percentB,
			squeeze,
			volatility,
			pricePosition,
			momentum,
			breakoutDirection
		}
	}

	/**
	 * Generate comprehensive trading decision
	 */
	private generateTradingDecision(
		analysis: BollingerAnalysis,
		minConfidence: number,
		squeezeDetection: boolean,
		volatilityBreakout: boolean,
		meanReversion: boolean
	): TradingDecision {
		let shouldTrade = false
		let direction: 'high' | 'low' | undefined
		let confidence = 0
		let reason = ''

		// Strategy 1: Volatility Breakout (after squeeze)
		if (volatilityBreakout && analysis.squeeze && analysis.breakoutDirection !== 'none') {
			shouldTrade = true
			direction = analysis.breakoutDirection === 'up' ? 'high' : 'low'
			confidence = 0.8
			reason = `Volatility breakout ${analysis.breakoutDirection} after squeeze`
		}

		// Strategy 2: Mean Reversion
		if (!shouldTrade && meanReversion) {
			if (analysis.pricePosition === 'above_upper' && analysis.momentum !== 'bullish') {
				shouldTrade = true
				direction = 'low'
				confidence = 0.7
				reason = `Mean reversion - price above upper band (${analysis.percentB.toFixed(2)})`
			} else if (analysis.pricePosition === 'below_lower' && analysis.momentum !== 'bearish') {
				shouldTrade = true
				direction = 'high'
				confidence = 0.7
				reason = `Mean reversion - price below lower band (${analysis.percentB.toFixed(2)})`
			}
		}

		// Strategy 3: Trend Following (high volatility breakouts)
		if (!shouldTrade && analysis.volatility === 'high') {
			if (analysis.pricePosition === 'above_upper' && analysis.momentum === 'bullish') {
				shouldTrade = true
				direction = 'high'
				confidence = 0.65
				reason = `Trend following - strong bullish breakout`
			} else if (analysis.pricePosition === 'below_lower' && analysis.momentum === 'bearish') {
				shouldTrade = true
				direction = 'low'
				confidence = 0.65
				reason = `Trend following - strong bearish breakout`
			}
		}

		// Strategy 4: Band Touch Signals (moderate confidence)
		if (!shouldTrade) {
			if (analysis.pricePosition === 'upper_zone' && analysis.percentB > 0.8) {
				shouldTrade = true
				direction = 'low'
				confidence = 0.6
				reason = `Upper band approach - potential reversal`
			} else if (analysis.pricePosition === 'lower_zone' && analysis.percentB < 0.2) {
				shouldTrade = true
				direction = 'high'
				confidence = 0.6
				reason = `Lower band approach - potential reversal`
			}
		}

		// Apply modifiers
		if (shouldTrade) {
			// Volatility modifier
			if (analysis.volatility === 'high') {
				confidence += 0.1
				reason += ` (high volatility)`
			} else if (analysis.volatility === 'low') {
				confidence -= 0.1
				reason += ` (low volatility)`
			}

			// Momentum modifier
			if (
				(direction === 'high' && analysis.momentum === 'bullish') ||
				(direction === 'low' && analysis.momentum === 'bearish')
			) {
				confidence += 0.1
				reason += ` with confirming momentum`
			} else if (
				(direction === 'high' && analysis.momentum === 'bearish') ||
				(direction === 'low' && analysis.momentum === 'bullish')
			) {
				confidence -= 0.15
				reason += ` against momentum`
			}

			// %B position modifier (extreme positions get higher confidence)
			if (analysis.percentB > 1.1 || analysis.percentB < -0.1) {
				confidence += 0.1
				reason += ` (extreme position)`
			}

			// Squeeze detection modifier
			if (squeezeDetection && analysis.squeeze && analysis.breakoutDirection === 'none') {
				confidence -= 0.2
				reason += ` (in squeeze - reduced confidence)`
			}
		}

		// Ensure confidence is within bounds
		confidence = Math.max(0, Math.min(1, confidence))

		// Final decision
		if (!shouldTrade || confidence < minConfidence) {
			return {
				shouldTrade: false,
				reason: shouldTrade
					? `${reason} - confidence ${confidence.toFixed(2)} below minimum ${minConfidence}`
					: `No clear Bollinger Bands signal (Price: ${analysis.currentPrice.toFixed(
							4
					  )}, %B: ${analysis.percentB.toFixed(2)}, Position: ${analysis.pricePosition})`
			}
		}

		return {
			shouldTrade: true,
			direction,
			confidence,
			reason: `${reason} - confidence: ${confidence.toFixed(2)}`
		}
	}
}
