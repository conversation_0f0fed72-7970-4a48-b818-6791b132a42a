# Screenshot-Based Price Extraction

## Overview

The trading bot now includes screenshot-based price extraction as a fallback method when traditional DOM selectors fail to access real-time price data from Pocket Option's chart. This feature uses Optical Character Recognition (OCR) to read price values directly from screenshots of the trading interface.

## Why Screenshot-Based Extraction?

Traditional web scraping methods rely on DOM selectors to extract price data from HTML elements. However, modern trading platforms like Pocket Option often:

- Use canvas-based charts that don't expose price data in the DOM
- Implement dynamic content loading that makes selectors unreliable
- Use complex JavaScript frameworks that obfuscate data access
- Have anti-scraping measures that prevent direct data extraction

Screenshot-based extraction solves these issues by:

- Reading visual price displays directly from the screen
- Working regardless of the underlying technology
- Being resistant to DOM structure changes
- Providing a reliable fallback when other methods fail

## How It Works

1. **Screenshot Capture**: The bot takes a screenshot of the browser window
2. **Area Extraction**: Extracts the specific area where price data is displayed
3. **Image Preprocessing**: Enhances the image for better OCR accuracy:
   - Converts to grayscale
   - Adjusts brightness and contrast
   - Applies threshold filtering
   - Resizes for optimal OCR processing
4. **OCR Processing**: Uses Tesseract.js to extract text from the processed image
5. **Price Parsing**: Extracts numeric price values from the OCR text using pattern matching

## Configuration

### Enabling Screenshot Extraction

1. Open the bot settings panel
2. Navigate to the "Screenshot Price Extraction" section
3. Check "Enable Screenshot Price Extraction"
4. Optionally enable "Debug Mode" to save screenshots for analysis

### Price Area Configuration

You can manually configure the screen area where price data is displayed:

- **X Position**: Horizontal position of the price area (pixels from left)
- **Y Position**: Vertical position of the price area (pixels from top)
- **Width**: Width of the price area in pixels
- **Height**: Height of the price area in pixels

Leave these fields empty for automatic detection.

### Default Settings

If no manual configuration is provided, the bot uses these default areas:

```typescript
// Default price area - typically in the top-right of the chart
priceArea: { x: 900, y: 120, width: 200, height: 50 }
```

## Auto-Calibration

The bot includes an auto-calibration feature that attempts to find the optimal price area:

```typescript
// Call this method to auto-calibrate
await bot.calibrateScreenshotExtractor()
```

This feature:
- Tests multiple predefined areas
- Attempts OCR on each area
- Selects the area that successfully extracts a valid price
- Updates the configuration automatically

## Debug Mode

When debug mode is enabled, the bot saves screenshots to help with troubleshooting:

- `full_[timestamp].png`: Complete browser screenshot
- `price_area_[timestamp].png`: Extracted price area
- `processed_[timestamp].png`: Preprocessed image sent to OCR

These files are saved in the `./screenshots` directory.

## Performance Considerations

Screenshot-based extraction is more resource-intensive than DOM-based methods:

- **CPU Usage**: OCR processing requires significant CPU resources
- **Memory Usage**: Image processing and OCR workers use additional memory
- **Speed**: Screenshot extraction is slower than DOM selectors (typically 1-3 seconds)
- **Accuracy**: OCR accuracy depends on image quality and price display clarity

## Best Practices

1. **Use as Fallback**: Enable screenshot extraction as a fallback when DOM methods fail
2. **Optimize Price Area**: Configure the smallest possible price area for better performance
3. **Monitor Debug Output**: Use debug mode initially to verify correct price extraction
4. **Regular Calibration**: Recalibrate if Pocket Option's interface changes
5. **Resource Management**: Monitor system resources when using screenshot extraction

## Troubleshooting

### Common Issues

1. **No Price Detected**
   - Enable debug mode to check saved screenshots
   - Verify the price area configuration
   - Ensure the price is clearly visible on screen
   - Try auto-calibration

2. **Incorrect Price Values**
   - Check if multiple numbers are visible in the price area
   - Adjust the price area to focus only on the current price
   - Verify the price format matches expected patterns

3. **Performance Issues**
   - Reduce the price area size
   - Disable debug mode in production
   - Consider increasing the price check interval

### Error Messages

- `Screenshot extractor not initialized`: Enable screenshot extraction in settings
- `OCR worker not initialized`: Check if Tesseract.js dependencies are installed
- `Could not find optimal price area`: Manual configuration may be required

## Technical Details

### Dependencies

- **Tesseract.js**: OCR engine for text recognition
- **Sharp**: Image processing library for preprocessing
- **Playwright**: Browser automation and screenshot capture

### Price Pattern Recognition

The bot recognizes these price patterns:
- Forex prices: `1.23456` (4-6 decimal places)
- Stock prices: `123.45` (2-3 decimal places)
- Any decimal number: `\d+\.\d+`
- Whole numbers as fallback: `\d+`

### Image Preprocessing Pipeline

1. Extract price area from full screenshot
2. Resize to 400x100 pixels for optimal OCR
3. Convert to grayscale
4. Enhance brightness and saturation
5. Apply threshold filter (128)
6. Convert to PNG format

## Future Enhancements

Potential improvements for screenshot-based extraction:

1. **Machine Learning**: Train a custom model for price recognition
2. **Multiple Areas**: Support multiple price areas for different assets
3. **Real-time Optimization**: Dynamic adjustment of preprocessing parameters
4. **Caching**: Cache processed images to improve performance
5. **Alternative OCR Engines**: Support for other OCR libraries
