import { PriceData } from '../../../../shared/types'
import { TradingStrategy, TradingDecision } from './TradingStrategy'

interface SlideConfig {
	trendThreshold: number // Minimum percentage change to consider a trend
	momentumPeriod: number // Number of periods to calculate momentum
	dynamicStopLoss: boolean // Enable dynamic stop loss adjustment
	trailDistance: number // Trailing stop distance as percentage
	minTrendStrength: number // Minimum trend strength (0-1)
	breakoutConfirmation: boolean // Require breakout confirmation
	volumeFilter: boolean // Consider volume in decisions (if available)
}

interface TrendAnalysis {
	direction: 'up' | 'down' | 'sideways'
	strength: number // 0-1
	momentum: number // Rate of change
	duration: number // How long trend has been active
	reliability: number // How consistent the trend is
	breakoutLevel?: number // Price level that confirms breakout
}

export class SlideStrategy extends TradingStrategy {
	private readonly COOLDOWN_PERIOD = 15000 // 15 seconds between trades
	private lastTradeTime = 0
	private currentTrend: TrendAnalysis | null = null
	private trendHistory: TrendAnalysis[] = []
	private entryPrice: number = 0
	private stopLossLevel: number = 0
	private isInPosition: boolean = false

	getName(): string {
		return 'Slide Strategy'
	}

	getDescription(): string {
		return 'Follows trending movements with dynamic stop-losses and momentum analysis'
	}

	async evaluate(priceData: PriceData): Promise<TradingDecision> {
		// Add current price data to history
		this.addPriceData(priceData)

		// Configuration with defaults
		const config: SlideConfig = {
			trendThreshold: this.config.trendThreshold || 0.2, // 0.2% minimum trend
			momentumPeriod: this.config.momentumPeriod || 10,
			dynamicStopLoss: this.config.dynamicStopLoss !== false,
			trailDistance: this.config.trailDistance || 0.15, // 0.15% trailing stop
			minTrendStrength: this.config.minTrendStrength || 0.6,
			breakoutConfirmation: this.config.breakoutConfirmation !== false,
			volumeFilter: this.config.volumeFilter || false
		}

		// Check cooldown period
		const now = Date.now()
		if (now - this.lastTradeTime < this.COOLDOWN_PERIOD) {
			return {
				shouldTrade: false,
				reason: `Cooldown period active (${Math.round(
					(this.COOLDOWN_PERIOD - (now - this.lastTradeTime)) / 1000
				)}s remaining)`
			}
		}

		// Need sufficient data
		if (this.priceHistory.length < config.momentumPeriod + 5) {
			return {
				shouldTrade: false,
				reason: `Insufficient data for slide analysis (need ${config.momentumPeriod + 5}+ points, have ${
					this.priceHistory.length
				})`
			}
		}

		// Analyze current trend
		const trendAnalysis = this.analyzeTrend(config)
		this.currentTrend = trendAnalysis

		// Update dynamic stop loss if in position
		if (this.isInPosition && config.dynamicStopLoss) {
			this.updateDynamicStopLoss(priceData.current, config)
		}

		// Generate trading decision
		const decision = this.generateSlideDecision(priceData, trendAnalysis, config)

		// Update last trade time if we're making a trade
		if (decision.shouldTrade) {
			this.lastTradeTime = now
			this.entryPrice = priceData.current
			this.isInPosition = true
			this.setInitialStopLoss(priceData.current, decision.direction!, config)
		}

		return decision
	}

	private analyzeTrend(config: SlideConfig): TrendAnalysis {
		const prices = this.priceHistory.slice(-config.momentumPeriod * 2).map(p => p.current)

		if (prices.length < config.momentumPeriod) {
			return {
				direction: 'sideways',
				strength: 0,
				momentum: 0,
				duration: 0,
				reliability: 0
			}
		}

		// Calculate trend using linear regression
		const trendLine = this.calculateTrendLine(prices)
		const direction = trendLine.slope > 0 ? 'up' : trendLine.slope < 0 ? 'down' : 'sideways'

		// Calculate momentum
		const momentum = this.calculateMomentum(config.momentumPeriod)

		// Calculate trend strength based on R-squared and momentum
		const strength = Math.min(1, trendLine.rSquared * Math.abs(momentum) * 10)

		// Calculate trend duration
		const duration = this.calculateTrendDuration(direction)

		// Calculate reliability based on consistency
		const reliability = this.calculateTrendReliability(prices, trendLine)

		// Detect breakout levels
		const breakoutLevel = this.detectBreakoutLevel(prices)

		return {
			direction,
			strength,
			momentum,
			duration,
			reliability,
			breakoutLevel
		}
	}

	private calculateTrendLine(prices: number[]): { slope: number; intercept: number; rSquared: number } {
		const n = prices.length
		const x = Array.from({ length: n }, (_, i) => i)

		const sumX = x.reduce((sum, val) => sum + val, 0)
		const sumY = prices.reduce((sum, val) => sum + val, 0)
		const sumXY = x.reduce((sum, val, i) => sum + val * prices[i], 0)
		const sumXX = x.reduce((sum, val) => sum + val * val, 0)
		const sumYY = prices.reduce((sum, val) => sum + val * val, 0)

		const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX)
		const intercept = (sumY - slope * sumX) / n

		// Calculate R-squared
		const yMean = sumY / n
		const ssRes = prices.reduce((sum, val, i) => {
			const predicted = slope * i + intercept
			return sum + Math.pow(val - predicted, 2)
		}, 0)
		const ssTot = prices.reduce((sum, val) => sum + Math.pow(val - yMean, 2), 0)
		const rSquared = ssTot > 0 ? 1 - ssRes / ssTot : 0

		return { slope, intercept, rSquared: Math.max(0, rSquared) }
	}

	private calculateMomentum(periods: number): number {
		if (this.priceHistory.length < periods) return 0

		const recent = this.priceHistory.slice(-periods)
		const firstPrice = recent[0].current
		const lastPrice = recent[recent.length - 1].current

		return (lastPrice - firstPrice) / firstPrice
	}

	private calculateTrendDuration(direction: 'up' | 'down' | 'sideways'): number {
		let duration = 0

		// Count consecutive periods in same direction
		for (let i = this.priceHistory.length - 1; i > 0; i--) {
			const current = this.priceHistory[i].current
			const previous = this.priceHistory[i - 1].current

			const currentDirection = current > previous ? 'up' : current < previous ? 'down' : 'sideways'

			if (currentDirection === direction) {
				duration++
			} else {
				break
			}
		}

		return duration
	}

	private calculateTrendReliability(prices: number[], trendLine: { slope: number; intercept: number }): number {
		// Calculate how well prices follow the trend line
		let deviations = 0
		const threshold = 0.001 // 0.1% deviation threshold

		for (let i = 0; i < prices.length; i++) {
			const expected = trendLine.slope * i + trendLine.intercept
			const actual = prices[i]
			const deviation = Math.abs(actual - expected) / expected

			if (deviation > threshold) {
				deviations++
			}
		}

		return Math.max(0, 1 - deviations / prices.length)
	}

	private detectBreakoutLevel(prices: number[]): number | undefined {
		if (prices.length < 20) return undefined

		// Find recent support/resistance levels
		const recentPrices = prices.slice(-20)
		const highs = []
		const lows = []

		for (let i = 1; i < recentPrices.length - 1; i++) {
			if (recentPrices[i] > recentPrices[i - 1] && recentPrices[i] > recentPrices[i + 1]) {
				highs.push(recentPrices[i])
			}
			if (recentPrices[i] < recentPrices[i - 1] && recentPrices[i] < recentPrices[i + 1]) {
				lows.push(recentPrices[i])
			}
		}

		// Return the most recent significant level
		const currentPrice = prices[prices.length - 1]
		const resistance = highs.length > 0 ? Math.max(...highs) : undefined
		const support = lows.length > 0 ? Math.min(...lows) : undefined

		if (resistance && currentPrice < resistance && (resistance - currentPrice) / currentPrice < 0.005) {
			return resistance
		}
		if (support && currentPrice > support && (currentPrice - support) / support < 0.005) {
			return support
		}

		return undefined
	}

	private generateSlideDecision(priceData: PriceData, trend: TrendAnalysis, config: SlideConfig): TradingDecision {
		const currentPrice = priceData.current

		// Don't trade in sideways markets
		if (trend.direction === 'sideways') {
			return {
				shouldTrade: false,
				reason: 'Market is sideways, waiting for clear trend'
			}
		}

		// Check trend strength
		if (trend.strength < config.minTrendStrength) {
			return {
				shouldTrade: false,
				reason: `Trend strength ${(trend.strength * 100).toFixed(1)}% below minimum ${(
					config.minTrendStrength * 100
				).toFixed(1)}%`
			}
		}

		// Check momentum threshold
		if (Math.abs(trend.momentum) < config.trendThreshold / 100) {
			return {
				shouldTrade: false,
				reason: `Momentum ${(trend.momentum * 100).toFixed(2)}% below threshold ${config.trendThreshold}%`
			}
		}

		// Check breakout confirmation if required
		if (config.breakoutConfirmation && trend.breakoutLevel) {
			const breakoutDistance = Math.abs(currentPrice - trend.breakoutLevel) / trend.breakoutLevel
			if (breakoutDistance > 0.002) {
				// Must be within 0.2% of breakout level
				return {
					shouldTrade: false,
					reason: `Waiting for breakout confirmation at ${trend.breakoutLevel.toFixed(5)}`
				}
			}
		}

		// Determine trade direction
		const direction = trend.direction === 'up' ? 'high' : 'low'

		// Calculate confidence based on multiple factors
		let confidence = trend.strength * 0.4 + trend.reliability * 0.3 + Math.min(1, trend.duration / 10) * 0.3

		// Boost confidence for strong momentum
		if (Math.abs(trend.momentum) > (config.trendThreshold / 100) * 2) {
			confidence *= 1.2
		}

		// Boost confidence for breakout trades
		if (trend.breakoutLevel && Math.abs(currentPrice - trend.breakoutLevel) / trend.breakoutLevel < 0.001) {
			confidence *= 1.3
		}

		confidence = Math.min(1, confidence)

		return {
			shouldTrade: true,
			direction,
			confidence,
			reason: `Strong ${trend.direction} trend detected (strength: ${(trend.strength * 100).toFixed(1)}%, momentum: ${(
				trend.momentum * 100
			).toFixed(2)}%)`,
			metadata: {
				strategy: 'slide',
				trendStrength: trend.strength,
				momentum: trend.momentum,
				duration: trend.duration,
				reliability: trend.reliability,
				breakoutLevel: trend.breakoutLevel
			}
		}
	}

	private setInitialStopLoss(entryPrice: number, direction: 'high' | 'low', config: SlideConfig): void {
		const stopDistance = config.trailDistance / 100

		if (direction === 'high') {
			this.stopLossLevel = entryPrice * (1 - stopDistance)
		} else {
			this.stopLossLevel = entryPrice * (1 + stopDistance)
		}
	}

	private updateDynamicStopLoss(currentPrice: number, config: SlideConfig): void {
		if (!this.isInPosition) return

		const stopDistance = config.trailDistance / 100

		// Determine trade direction based on entry vs current price
		const isLongTrade = currentPrice > this.entryPrice

		if (isLongTrade) {
			// For long trades, move stop loss up with price
			const newStopLoss = currentPrice * (1 - stopDistance)
			if (newStopLoss > this.stopLossLevel) {
				this.stopLossLevel = newStopLoss
			}
		} else {
			// For short trades, move stop loss down with price
			const newStopLoss = currentPrice * (1 + stopDistance)
			if (newStopLoss < this.stopLossLevel) {
				this.stopLossLevel = newStopLoss
			}
		}
	}

	// Check if stop loss is hit
	isStopLossHit(currentPrice: number): boolean {
		if (!this.isInPosition) return false

		const isLongTrade = currentPrice > this.entryPrice

		if (isLongTrade) {
			return currentPrice <= this.stopLossLevel
		} else {
			return currentPrice >= this.stopLossLevel
		}
	}

	// Method to be called when position closes
	onPositionClose(): void {
		this.isInPosition = false
		this.entryPrice = 0
		this.stopLossLevel = 0
	}

	// Get current trend information for UI display
	getCurrentTrend(): TrendAnalysis | null {
		return this.currentTrend
	}

	// Get strategy status
	getStatus(): any {
		return {
			isInPosition: this.isInPosition,
			entryPrice: this.entryPrice,
			stopLossLevel: this.stopLossLevel,
			currentTrend: this.currentTrend,
			lastTradeTime: this.lastTradeTime,
			cooldownRemaining: Math.max(0, this.COOLDOWN_PERIOD - (Date.now() - this.lastTradeTime))
		}
	}
}
