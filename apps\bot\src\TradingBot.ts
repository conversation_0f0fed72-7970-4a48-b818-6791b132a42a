import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, chromium } from 'playwright'
import { EventEmitter } from 'events'
import { BotConfig, BotStatus, PriceData, TradeResult, TradingSettings } from '../../../shared/types'
import { TradingStrategy } from './strategies/TradingStrategy'
import { StrategyFactory } from './strategies/StrategyFactory'
import { ScreenshotPriceExtractor } from './ScreenshotPriceExtractor'
import { WebSocketPriceFeed } from './services/WebSocketPriceFeed'
import fs from 'fs'

export class TradingBot extends EventEmitter {
	private browser: Browser | BrowserContext | null = null
	private page: Page | null = null
	private isRunning = false
	private config: BotConfig
	private status: BotStatus
	private priceHistory: PriceData[] = []
	private trades: TradeResult[] = []
	private priceCheckInterval: NodeJS.Timeout | null = null
	private strategy: TradingStrategy
	private lastBalanceUpdateTime: number | null = null
	private screenshotExtractor: ScreenshotPriceExtractor | null = null
	private webSocketPriceFeed: WebSocketPriceFeed | null = null
	private webSocketConnected: boolean = false

	constructor(config: BotConfig) {
		super()
		this.config = config
		this.status = {
			isRunning: false,
			currentPrice: 0,
			tradesCount: 0,
			winCount: 0,
			lossCount: 0,
			winRate: 0,
			totalProfit: 0,
			accountBalance: 0,
			lastTrade: null
		}

		// Initialize strategy
		this.strategy = StrategyFactory.createStrategy(this.config.settings.strategy, this.config.settings.strategyConfig)
	}

	async start(): Promise<void> {
		if (this.isRunning) {
			throw new Error('Bot is already running')
		}

		try {
			this.log('info', 'Starting trading bot...')

			// Use existing browser if provided, otherwise launch new one
			if (this.config.existingBrowser && this.config.existingPage) {
				this.log('info', 'Using existing browser context...')
				this.browser = this.config.existingBrowser
				this.page = this.config.existingPage

				// Verify the page is still valid and on the correct URL
				try {
					const currentUrl = this.page?.url()
					if (currentUrl && !currentUrl.includes('pocketoption.com')) {
						this.log('info', 'Navigating to Pocket Option...')
						await this.page?.goto(this.config.pocketOptionUrl)
						await this.page?.waitForLoadState('domcontentloaded', { timeout: 30000 })
					}
				} catch (error) {
					this.log('warn', 'Error checking page URL, proceeding anyway...')
				}
			} else {
				this.log('info', 'Launching new browser context...')
				// Launch persistent browser context
				if (this.config.userDataDir) {
					const context = await chromium.launchPersistentContext(this.config.userDataDir, {
						headless: this.config.headless,
						viewport: null
					})
					this.browser = context
					// Get the first page or create a new one
					const pages = context.pages()
					this.page = pages.length > 0 ? pages[0] : await context.newPage()
				} else {
					// Fallback to regular browser launch
					const browser = await chromium.launch({
						headless: this.config.headless
					})
					this.browser = browser
					this.page = await browser.newPage()
				}

				// Navigate to Pocket Option
				this.log('info', 'Navigating to Pocket Option...')
				await this.page?.goto(this.config.pocketOptionUrl)

				// Wait for initial page load with longer timeout
				this.log('info', 'Waiting for page to load... Please complete any captcha and sign in.')
				await this.page?.waitForLoadState('domcontentloaded', { timeout: 120000 }) // 2 minutes

				// Wait for user to sign in - look for trading interface elements
				this.log('info', 'Please sign in to your Pocket Option account. The bot will wait for you to complete login.')

				// Wait for trading interface to be available (this indicates successful login)
				try {
					// Wait for any trading elements to appear first
					await this.page?.waitForSelector('body, .main-content, .trading-interface, .header, .menu', {
						timeout: 300000 // 5 minutes for login
					})
				} catch (error) {
					throw new Error('Setup timeout: Please ensure you are logged in to Pocket Option and on the trading page.')
				}
			}

			this.log('info', 'Setting up trading environment...')

			// Wait a bit for the page to fully load
			await this.page?.waitForTimeout(3000)

			// Check if already in demo mode before switching
			const isAlreadyDemo = await this.checkIfInDemoMode()
			if (isAlreadyDemo) {
				this.log('info', 'Already in demo mode. Skipping demo account switch.')
			} else {
				this.log('info', 'Not in demo mode. Switching to demo account...')
				await this.switchToDemoAccount()
			}

			// Try to select a trading asset
			await this.selectTradingAsset()

			// Wait for trading interface to be ready
			await this.waitForTradingInterface()

			this.log('info', 'Trading interface setup complete.')

			// Initialize screenshot-based price extraction
			try {
				const screenshotDir = './test-screenshots'
				// Ensure screenshot directory exists
				if (!fs.existsSync(screenshotDir)) {
					fs.mkdirSync(screenshotDir, { recursive: true })
				}

				if (this.page) {
					this.screenshotExtractor = new ScreenshotPriceExtractor(this.page, {
						debugMode: true, // Enable debug mode initially
						screenshotPath: screenshotDir
					})
					await this.screenshotExtractor.initialize()
					this.log('info', 'Screenshot price extractor initialized')

					// Auto-calibrate on startup
					const calibrationSuccess = await this.calibrateScreenshotExtractor()
					if (calibrationSuccess) {
						this.log('info', 'Screenshot extractor auto-calibrated successfully')
					} else {
						this.log('warn', 'Screenshot extractor auto-calibration failed, will try again during price checks')
					}
				} else {
					this.log('error', 'Cannot initialize screenshot extractor: page is null')
				}
			} catch (error) {
				this.log('error', `Error initializing screenshot price extractor: ${error}`)
			}

			// Initialize WebSocket price feed for enhanced price data
			if (this.page) {
				this.webSocketPriceFeed = new WebSocketPriceFeed(this.page, {
					reconnectInterval: 5000,
					maxReconnectAttempts: 10,
					priceValidationRange: { min: 0.001, max: 1000000 },
					enableLogging: true
				})

				// Set up price update listener
				this.webSocketPriceFeed.on('priceUpdate', (priceData: PriceData) => {
					this.webSocketConnected = true
					this.handleWebSocketPriceUpdate(priceData)
				})

				// Set up reconnection listeners
				this.webSocketPriceFeed.on('disconnected', () => {
					this.webSocketConnected = false
					this.log('warn', 'WebSocket price feed disconnected')
				})

				this.webSocketPriceFeed.on('reconnected', () => {
					this.webSocketConnected = true
					this.log('info', 'WebSocket price feed reconnected')
				})

				const wsInitialized = await this.webSocketPriceFeed.initialize()
				if (wsInitialized) {
					this.log('info', 'WebSocket price feed initialized successfully')
				} else {
					this.log('warn', 'WebSocket price feed initialization failed, falling back to DOM extraction')
				}
			}

			this.isRunning = true
			this.status.isRunning = true
			this.status.startTime = Date.now()

			// Get initial account balance
			await this.updateAccountBalance()

			// Start price monitoring only if WebSocket is not connected
			if (!this.webSocketConnected) {
				this.startPriceMonitoring()
			}

			this.log('info', 'Trading bot started successfully')
			this.emitStatusUpdate()
		} catch (error) {
			this.log('error', `Failed to start bot: ${error}`)
			await this.stop()
			throw error
		}
	}

	async stop(): Promise<void> {
		this.log('info', 'Stopping trading bot...')

		this.isRunning = false
		this.status.isRunning = false

		// Always clear interval before nulling page/browser
		if (this.priceCheckInterval) {
			clearInterval(this.priceCheckInterval)
			this.priceCheckInterval = null
		}

		// Cleanup screenshot extractor
		if (this.screenshotExtractor) {
			await this.screenshotExtractor.cleanup()
			this.screenshotExtractor = null
			this.log('info', 'Screenshot price extractor cleaned up')
		}

		// Cleanup WebSocket price feed
		if (this.webSocketPriceFeed) {
			this.webSocketPriceFeed.disconnect()
			this.webSocketPriceFeed = null
			this.log('info', 'WebSocket price feed disconnected')
		}

		// Only close browser if we created it (not using existing instance)
		if (this.browser && !this.config.existingBrowser) {
			await this.browser.close()
			this.browser = null
			this.page = null
		} else if (this.config.existingBrowser) {
			// Keep browser open but clear references
			this.browser = null
			this.page = null
			this.log('info', 'Browser instance kept open for reuse')
		}

		this.log('info', 'Trading bot stopped')
		this.emitStatusUpdate()
	}

	private startPriceMonitoring(): void {
		if (this.priceCheckInterval) return
		this.priceCheckInterval = setInterval(async () => {
			try {
				// Only poll if WebSocket is not connected
				if (!this.webSocketConnected) {
					await this.checkPrice()
				}
				const now = Date.now()
				if (now - (this.lastBalanceUpdateTime ?? 0) >= 30_000) {
					this.lastBalanceUpdateTime = now
					await this.updateAccountBalance()
				}
			} catch (error) {
				this.log('error', `Price monitoring error: ${error}`)
			}
		}, 1000) // Check price every second
	}

	private handleWebSocketPriceUpdate(priceData: PriceData): void {
		if (!this.isRunning) return
		// Stop DOM polling if WebSocket is connected
		if (this.priceCheckInterval) {
			clearInterval(this.priceCheckInterval)
			this.priceCheckInterval = null
		}
		// Add to price history
		this.priceHistory.push(priceData)
		if (this.priceHistory.length > 100) {
			this.priceHistory.shift()
		}
		// Update status
		this.status.currentPrice = priceData.current
		this.emit('price-update', priceData)
		// Evaluate trading opportunity if auto trading is enabled
		if (this.config.settings.autoTrade) {
			this.evaluateTradeOpportunity(priceData).catch(error => {
				this.log('error', `Error evaluating trade opportunity from WebSocket: ${error}`)
			})
		}
	}

	private async checkPrice(): Promise<void> {
		if (!this.page || !this.isRunning) return

		try {
			// Check if page is still valid
			if (this.page.isClosed()) {
				this.log('warn', 'Page is closed, stopping price monitoring')
				return
			}

			if (!this.webSocketConnected) {
				this.log(
					'warn',
					'WebSocket price feed is not connected. Check if the site has changed or if WebSocket extraction is blocked.'
				)
			}

			// Updated selectors based on actual Pocket Option DOM analysis
			const priceSelectors = [
				// Try to get price from chart canvas or SVG elements
				'canvas[data-price]',
				'svg [data-price]',
				'.chart-container [data-price]',
				// Look for price in trading widget areas
				'.trading-widget .price',
				'.trade-panel .price',
				'.current-rate',
				'.live-rate',
				// Pocket Option specific selectors (observed from actual site)
				'.asset-price-display',
				'.quote-display',
				'.rate-display',
				// Chart library specific selectors
				'.tradingview-widget .price',
				'.tv-symbol-price-quote',
				'.tv-symbol-price',
				// Generic price patterns with better specificity
				'[class*="price"][class*="current"]',
				'[class*="rate"][class*="live"]',
				'[id*="current-price"]',
				'[id*="live-price"]',
				// Fallback to original selectors
				'.price-value',
				'.current-price',
				'.asset-price',
				'.quote-value'
			]

			let priceElement = null
			for (const selector of priceSelectors) {
				priceElement = await this.page.$(selector)
				if (priceElement) {
					this.log('info', `Found price element with selector: ${selector}`)
					break
				}
			}

			if (!priceElement) {
				this.log('warn', 'No price element found with known selectors. Attempting canvas/alternative extraction.')
				// Log all elements with numeric content for debugging
				const potentialPriceElements = await this.page.$$eval('*', elements => {
					return elements
						.filter(el => {
							const text = el.textContent?.trim() || ''
							return /\d+\.?\d*/.test(text) && text.length >= 3 && text.length <= 10
						})
						.slice(0, 10)
						.map(el => ({
							text: el.textContent?.trim(),
							tagName: el.tagName,
							className: el.className,
							id: el.id
						}))
				})
				if (potentialPriceElements.length > 0) {
					this.log('info', `Potential price elements for selector update: ${JSON.stringify(potentialPriceElements)}`)
				}
				// Try to extract price from chart canvas or other sources
				const canvasPrice = await this.extractPriceFromCanvas()
				if (canvasPrice > 0) {
					this.log('info', `Extracted price from canvas: ${canvasPrice}`)
					const previousPrice = this.status.currentPrice
					const priceData: PriceData = {
						current: canvasPrice,
						previous: previousPrice,
						timestamp: Date.now(),
						trend: canvasPrice > previousPrice ? 'up' : canvasPrice < previousPrice ? 'down' : 'neutral',
						change: canvasPrice - previousPrice,
						changePercent: previousPrice > 0 ? ((canvasPrice - previousPrice) / previousPrice) * 100 : 0
					}
					this.priceHistory.push(priceData)
					if (this.priceHistory.length > 100) {
						this.priceHistory.shift()
					}
					this.status.currentPrice = canvasPrice
					this.emit('price-update', priceData)
					if (this.config.settings.autoTrade) {
						await this.evaluateTradeOpportunity(priceData)
					}
					return
				}

				// Try screenshot-based price extraction as next fallback
				if (this.screenshotExtractor) {
					// Check if the price area is configured
					const screenshotConfig = this.screenshotExtractor['config'] // Access internal config
					if (
						!screenshotConfig.priceArea ||
						typeof screenshotConfig.priceArea.x !== 'number' ||
						typeof screenshotConfig.priceArea.width !== 'number'
					) {
						// Price area not configured - try calibration
						this.log('info', 'Screenshot price area not configured, attempting calibration...')
						await this.calibrateScreenshotExtractor()
					}

					const screenshotPrice = await this.extractPriceFromScreenshot()
					if (screenshotPrice > 0) {
						this.log('info', `Extracted price from screenshot: ${screenshotPrice}`)
						const previousPrice = this.status.currentPrice
						const priceData: PriceData = {
							current: screenshotPrice,
							previous: previousPrice,
							timestamp: Date.now(),
							trend: screenshotPrice > previousPrice ? 'up' : screenshotPrice < previousPrice ? 'down' : 'neutral',
							change: screenshotPrice - previousPrice,
							changePercent: previousPrice > 0 ? ((screenshotPrice - previousPrice) / previousPrice) * 100 : 0
						}
						this.priceHistory.push(priceData)
						if (this.priceHistory.length > 100) {
							this.priceHistory.shift()
						}
						this.status.currentPrice = screenshotPrice
						this.emit('price-update', priceData)
						// Evaluate trading opportunity only once per price update
						if (this.config.settings.autoTrade) {
							await this.evaluateTradeOpportunity(priceData)
						}
						// Exit the method to avoid duplicate price data
						return
					}
				}

				// Try alternative price extraction methods as last resort
				const alternativePrice = await this.extractPriceFromAlternativeSources()
				if (alternativePrice > 0) {
					this.log('warn', `Fallback: Extracted price from alternative sources: ${alternativePrice}`)
					const previousPrice = this.status.currentPrice
					const priceData: PriceData = {
						current: alternativePrice,
						previous: previousPrice,
						timestamp: Date.now(),
						trend: alternativePrice > previousPrice ? 'up' : alternativePrice < previousPrice ? 'down' : 'neutral',
						change: alternativePrice - previousPrice,
						changePercent: previousPrice > 0 ? ((alternativePrice - previousPrice) / previousPrice) * 100 : 0
					}
					this.priceHistory.push(priceData)
					if (this.priceHistory.length > 100) {
						this.priceHistory.shift()
					}
					this.status.currentPrice = alternativePrice
					this.emit('price-update', priceData)
					if (this.config.settings.autoTrade) {
						await this.evaluateTradeOpportunity(priceData)
					}
					return
				}

				// Only run debug and show warning every 30 seconds to avoid spam
				const now = Date.now()
				if (!this.lastPriceWarningTime || now - this.lastPriceWarningTime > 30000) {
					this.lastPriceWarningTime = now
					await this.debugPriceElements()
					this.log(
						'warn',
						'Price element not found. Please ensure you are on the trading page and the chart is loaded.'
					)
				}
				return
			}

			const priceText = await priceElement.textContent()
			const currentPrice = parseFloat(priceText?.replace(/[^\d.-]/g, '') || '0')
			if (currentPrice > 0) {
				this.log('info', `Extracted price from DOM: ${currentPrice}`)
				const previousPrice = this.status.currentPrice
				const priceData: PriceData = {
					current: currentPrice,
					previous: previousPrice,
					timestamp: Date.now(),
					trend: currentPrice > previousPrice ? 'up' : currentPrice < previousPrice ? 'down' : 'neutral',
					change: currentPrice - previousPrice,
					changePercent: previousPrice > 0 ? ((currentPrice - previousPrice) / previousPrice) * 100 : 0
				}
				this.priceHistory.push(priceData)
				if (this.priceHistory.length > 100) {
					this.priceHistory.shift()
				}
				this.status.currentPrice = currentPrice
				this.emit('price-update', priceData)
				if (this.config.settings.autoTrade) {
					await this.evaluateTradeOpportunity(priceData)
				}
			}
		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : String(error)
			// Handle specific errors
			if (errorMessage.includes('Execution context was destroyed')) {
				this.log('warn', 'Page navigation detected, waiting for page to stabilize...')
				await new Promise(resolve => setTimeout(resolve, 3000))
				return
			}
			if (errorMessage.includes('Target closed')) {
				this.log('warn', 'Browser tab was closed')
				return
			}
			this.log('error', `Error checking price: ${errorMessage}`)
		}
	}

	private async evaluateTradeOpportunity(priceData: PriceData): Promise<void> {
		// Check stop loss conditions
		if (this.shouldStopTrading()) {
			this.log('warn', 'Stop loss triggered - stopping auto trading')
			// Disable auto trading
			this.config.settings.autoTrade = false
			this.emit('stop-loss-triggered', {
				totalProfit: this.status.totalProfit,
				stopLossConfig: this.config.settings.stopLoss
			})
			// Notify UI / other listeners that the bot state changed
			this.emitStatusUpdate()
			return
		}

		try {
			// Use the selected strategy to evaluate trade opportunity
			const decision = await this.strategy.evaluate(priceData)

			this.log('info', `Strategy decision: ${decision.reason}`)

			if (decision.shouldTrade && decision.direction) {
				this.log(
					'info',
					`Strategy recommends ${decision.direction} trade with confidence ${decision.confidence?.toFixed(2) || 'N/A'}`
				)
				await this.executeTrade(decision.direction, priceData)
			}
		} catch (error) {
			this.log('error', `Strategy evaluation error: ${error}`)
		}
	}

	private async executeTrade(direction: 'high' | 'low', priceData: PriceData): Promise<void> {
		if (!this.page) return

		try {
			this.log('info', `Executing ${direction} trade at price ${priceData.current}`)

			// Updated trading button selectors based on actual Pocket Option interface
			const buttonSelectors =
				direction === 'high'
					? [
							// Buy button selectors (observed from actual site)
							'img[alt*="Buy"]',
							'button:has-text("Buy")',
							'[data-direction="call"]',
							'[data-direction="high"]',
							'.btn-call',
							'.buy-btn',
							'.trade-btn-call',
							// Generic buy patterns
							'text=Buy', // or 'button:has-text("Buy")'
							'*[class*="buy"]:not([class*="sell"])',
							'*[onclick*="buy"]',
							'*[onclick*="call"]'
					  ]
					: [
							// Sell button selectors (observed from actual site)
							'img[alt*="Sell"]',
							'button:has-text("Sell")',
							'[data-direction="put"]',
							'[data-direction="low"]',
							'.btn-put',
							'.sell-btn',
							'.trade-btn-put',
							// Generic sell patterns
							'text=Sell', // or 'button:has-text("Sell")'
							'*[class*="sell"]:not([class*="buy"])',
							'*[onclick*="sell"]',
							'*[onclick*="put"]'
					  ]

			let button = null
			for (const selector of buttonSelectors) {
				button = await this.page.$(selector)
				if (button) {
					this.log('info', `Found ${direction} button with selector: ${selector}`)
					break
				}
			}

			if (!button) {
				this.log('error', `${direction} button not found. Please ensure you are on the trading interface.`)
				return
			}

			await button.click()
			this.log('info', `Clicked ${direction} button`)

			// Create trade record
			const trade: TradeResult = {
				id: `trade_${Date.now()}`,
				timestamp: Date.now(),
				direction,
				amount: this.config.settings.tradeAmount,
				entryPrice: priceData.current,
				result: 'pending',
				duration: 60 // Default 1 minute trade
			}

			this.trades.push(trade)
			this.status.tradesCount++
			this.status.lastTrade = trade

			this.emit('trade-result', trade)
			this.emitStatusUpdate()

			// Wait for trade result (simplified - in real implementation, monitor DOM for result)
			setTimeout(() => {
				this.checkTradeResult(trade.id)
			}, trade.duration * 1000)
		} catch (error) {
			this.log('error', `Error executing trade: ${error}`)
		}
	}

	private async checkTradeResult(tradeId: string): Promise<void> {
		// This is a simplified implementation
		// In reality, you'd monitor the DOM for trade results
		const trade = this.trades.find(t => t.id === tradeId)
		if (!trade) return

		// Simulate random result for now (replace with actual DOM monitoring)
		const isWin = Math.random() > 0.5
		trade.result = isWin ? 'win' : 'loss'
		trade.exitPrice = this.status.currentPrice
		trade.profit = isWin ? trade.amount * 0.8 : -trade.amount // 80% payout

		if (isWin) {
			this.status.winCount++
		} else {
			this.status.lossCount++
		}

		this.status.winRate = (this.status.winCount / this.status.tradesCount) * 100
		this.status.totalProfit += trade.profit
		this.status.lastTrade = trade

		this.emit('trade-result', trade)

		// Update account balance after trade result
		await this.updateAccountBalance()

		this.emitStatusUpdate()

		this.log('info', `Trade ${tradeId} result: ${trade.result}, profit: ${trade.profit}`)
	}
	private shouldStopTrading(): boolean {
		const stopLoss = this.config.settings.stopLoss

		// If stop loss is not enabled, continue trading
		if (!stopLoss?.enabled) {
			return false
		}

		const totalProfit = this.status.totalProfit

		// Don't trigger stop loss if no trades have been made yet
		if (this.trades.length === 0) {
			return false
		}

		if (stopLoss.mode === 'percentage') {
			// const totalInvested = this.status.tradesCount * this.config.settings.tradeAmount
			const totalInvested = this.trades.reduce((sum, t) => sum + t.amount, 0)

			const thresholdPercentage = stopLoss.percentage ?? 10

			if (totalInvested > 0) {
				const lossPercentage = Math.abs(totalProfit / totalInvested) * 100
				if (totalProfit < 0 && lossPercentage >= thresholdPercentage) {
					this.log(
						'warn',
						`Stop loss triggered: Loss percentage ${lossPercentage.toFixed(
							2
						)}% exceeds threshold ${thresholdPercentage}%`
					)
					return true
				}
			}
		} else if (stopLoss.mode === 'fixed') {
			const thresholdAmount = stopLoss.fixedAmount ?? 100
			// For fixed mode, stop if total loss exceeds the fixed amount
			if (totalProfit < 0 && Math.abs(totalProfit) >= thresholdAmount) {
				this.log(
					'warn',
					`Stop loss triggered: Total loss $${Math.abs(totalProfit).toFixed(2)} exceeds threshold $${thresholdAmount}`
				)
				return true
			}
		}

		return false
	}
	private emitStatusUpdate(): void {
		this.status.uptime = this.status.startTime ? Date.now() - this.status.startTime : 0
		this.emit('status-update', { ...this.status })
	}

	private log(level: 'info' | 'warn' | 'error', message: string): void {
		const logEntry = {
			level,
			message,
			timestamp: Date.now()
		}
		this.emit('log', logEntry)
		console.log(`[${level.toUpperCase()}] ${message}`)
	}

	private async updateAccountBalance(): Promise<void> {
		if (!this.page) return

		try {
			// Updated balance selectors based on actual Pocket Option DOM analysis
			// Observed: "QT Demo USD 242.19" in banner area
			const balanceSelectors = [
				// Primary demo balance selector
				'.balance-info-block__balance .js-balance-demo',
				'.js-balance-demo',

				// Banner area balance (observed pattern: "QT Demo USD 242.19")
				'banner *:contains("QT Demo")',
				'*:contains("QT Demo USD")',
				'*:contains("Demo USD")',

				// Generic balance patterns
				'[class*="balance"][class*="demo"]',
				'[data-balance]',
				'[data-demo-balance]',

				// Alternative selectors
				'.js-hd.js-balance-demo',
				'.balance-info-block__balance span',
				'[data-hd-show]',
				'.balance-demo',
				'.account-balance'
			]

			for (const selector of balanceSelectors) {
				try {
					const balanceElement = await this.page.$(selector)
					if (balanceElement) {
						// Try to get the balance from data-hd-show attribute first
						const balanceFromData = await balanceElement.getAttribute('data-hd-show')

						if (balanceFromData) {
							const balance = parseFloat(balanceFromData)
							if (!isNaN(balance)) {
								this.status.accountBalance = balance
								this.log('info', `Account balance updated from ${selector}: $${balance.toFixed(2)}`)
								return
							}
						}

						// Fallback to text content and extract numeric value
						const balanceText = await balanceElement.textContent()
						if (balanceText) {
							// Extract numeric value from text like "QT Demo USD 242.19"
							const balanceMatch = balanceText.match(/[\d,]+\.?\d*/g)
							if (balanceMatch) {
								const balance = parseFloat(balanceMatch[balanceMatch.length - 1].replace(/,/g, ''))
								if (!isNaN(balance) && balance > 0) {
									this.status.accountBalance = balance
									this.log('info', `Account balance updated from ${selector}: $${balance.toFixed(2)}`)
									return
								}
							}
						}
					}
				} catch (e) {
					// Continue to next selector
				}
			}

			// Try alternative selectors if the main ones don't work
			const alternativeSelectors = ['.balance-display', '.demo-balance', '.account-demo-balance']

			for (const selector of alternativeSelectors) {
				try {
					const element = await this.page.$(selector)
					if (element) {
						const balanceFromData = await element.getAttribute('data-hd-show')
						if (balanceFromData) {
							const balance = parseFloat(balanceFromData)
							if (!isNaN(balance)) {
								this.status.accountBalance = balance
								this.log('info', `Account balance updated from ${selector}: $${balance.toFixed(2)}`)
								return
							}
						}

						const balanceText = await element.textContent()
						if (balanceText) {
							const balance = parseFloat(balanceText.trim())
							if (!isNaN(balance)) {
								this.status.accountBalance = balance
								this.log('info', `Account balance updated from ${selector}: $${balance.toFixed(2)}`)
								return
							}
						}
					}
				} catch (e) {
					// Continue to next selector
				}
			}

			this.log('warn', 'Could not find or parse account balance from DOM')
		} catch (error) {
			this.log('error', `Error updating account balance: ${error}`)
		}
	}

	getStatus(): BotStatus {
		return { ...this.status }
	}

	updateSettings(settings: TradingSettings): void {
		this.config.settings = { ...settings }

		// Update strategy if it changed
		this.strategy = StrategyFactory.createStrategy(settings.strategy, settings.strategyConfig)

		this.log('info', 'Settings updated')
	}

	private async checkIfInDemoMode(): Promise<boolean> {
		if (!this.page) return false

		try {
			// Check for the demo mode indicator using Playwright selectors
			// Look for the specific demo indicator in the balance info block
			const balanceLabelElement = await this.page.$('.balance-info-block__label')
			if (balanceLabelElement) {
				const labelText = await balanceLabelElement.textContent()
				if (labelText && (labelText.includes('Demo') || labelText.includes('QT Demo'))) {
					this.log('info', `Demo mode detected: Found "${labelText.trim()}" in balance label`)
					return true
				}
			}

			// Alternative selectors for demo mode detection
			const demoIndicators = [
				'.demo-account-indicator',
				'[data-account-type="demo"]',
				'.balance-item[data-type="demo"]'
			]

			for (const selector of demoIndicators) {
				try {
					const element = await this.page.$(selector)
					if (element) {
						const text = await element.textContent()
						if (text && (text.includes('Demo') || text.includes('QT Demo'))) {
							this.log('info', `Demo mode detected: Found "${text.trim()}" with selector ${selector}`)
							return true
						}
					}
				} catch (e) {
					// Continue checking
				}
			}

			this.log('info', 'Demo mode check result: false (not in demo mode)')
			return false
		} catch (error) {
			this.log('warn', `Error checking demo mode: ${error}`)
			return false
		}
	}

	private async switchToDemoAccount(): Promise<void> {
		if (!this.page) return

		try {
			this.log('info', 'Attempting to switch to demo account...')

			// Common selectors for demo account switch
			const demoSelectors = [
				'.right-block__item.js-drop-down-modal-open[data-modal-id="balance"]',
				'[data-mode="demo"]',
				'.demo-btn',
				'.demo-account',
				'.account-demo',
				'button[contains(text(), "Demo")]',
				'[title*="demo" i]',
				'[aria-label*="demo" i]',
				'.balance-switcher .demo',
				'.account-switcher .demo'
			]

			const demoElement = '.balance-item[data-type="demo"]'

			let demoButton = null
			let demoAccountItem = null
			try {
				demoAccountItem = await this.page.$(demoElement)
				if (demoAccountItem) {
					this.log('info', `Found demo account item with selector: ${demoElement}`)
				}
			} catch (e) {
				this.log('warn', `Demo account item not found with selector: ${demoElement}`)
			}

			for (const selector of demoSelectors) {
				try {
					demoButton = await this.page.$(selector)
					if (demoButton) {
						this.log('info', `Found demo button with selector: ${selector}`)
						break
					}
				} catch (e) {
					// Continue to next selector
				}
			}

			if (demoButton) {
				await demoButton.click()
				this.log('info', 'Clicked demo account button')
				await this.page.waitForTimeout(2000) // Wait for switch

				if (demoAccountItem) {
					await demoAccountItem.click()
					this.log('info', 'Clicked demo account item')
					await this.page.waitForTimeout(2000) // Wait for switch
				}
			} else {
				this.log('warn', 'Demo account button not found. You may already be in demo mode.')
			}
		} catch (error) {
			this.log('warn', `Could not switch to demo account: ${error}`)
		}
	}

	public async selectTradingAsset(): Promise<void> {
		if (!this.page) return

		try {
			this.log('info', 'Attempting to select trading asset...')

			// Get asset preferences from settings
			const assetType = this.config.settings.assetType || 'currency'
			const asset = this.config.settings.asset || 'AUD/CAD OTC'
			const assetFilter = this.config.settings.assetFilter || 'ALL' // 'OTC' or 'ALL'

			this.log('info', `Asset preferences - Type: ${assetType}, Asset: ${asset}, Filter: ${assetFilter}`)

			// Try multiple selectors for the asset panel toggle
			const assetPanelToggleSelectors = [
				'.js-fav-panel-switcher',
				'[data-fav-panel-switcher]',
				'.asset-panel-toggle',
				'.asset-switcher',
				'.fav-panel-switcher',
				'.asset-selector-toggle',
				'.trading-asset-selector',
				// Look for star icon or similar elements that might be the toggle
				'.fa-star',
				'[class*="star"]',
				'[class*="fav"]',
				'[class*="asset"]',
				// Generic selectors for clickable elements near the asset display
				'.current-asset',
				'.selected-asset',
				'.asset-display'
			]

			const assetsBlock = '.assets-block'
			let assetSelectionToggle = null

			// Try to find the asset panel toggle
			for (const selector of assetPanelToggleSelectors) {
				try {
					assetSelectionToggle = await this.page.$(selector)
					if (assetSelectionToggle) {
						this.log('info', `Found asset panel toggle with selector: ${selector}`)
						break
					}
				} catch (e) {
					// Continue to next selector
				}
			}

			// If we still can't find it, let's debug what's available
			if (!assetSelectionToggle) {
				this.log('info', 'Asset panel toggle not found with known selectors. Debugging available elements...')

				// Look for elements that might be the asset selector
				const potentialAssetElements = await this.page.$$eval('*', elements => {
					return elements
						.filter(el => {
							const text = el.textContent?.trim() || ''
							const className = el.className || ''
							const id = el.id || ''

							// Look for elements that might be related to assets
							return (
								text.includes('AUD/CAD') ||
								text.includes('EUR/USD') ||
								text.includes('OTC') ||
								className.includes('asset') ||
								className.includes('fav') ||
								className.includes('star') ||
								className.includes('switcher') ||
								id.includes('asset') ||
								id.includes('fav')
							)
						})
						.slice(0, 10) // Limit to first 10 matches
						.map(el => ({
							text: el.textContent?.trim(),
							tagName: el.tagName,
							className: el.className,
							id: el.id,
							clickable: el.tagName === 'BUTTON' || el.onclick !== null || el.style.cursor === 'pointer'
						}))
				})

				if (potentialAssetElements.length > 0) {
					this.log('info', `Found potential asset-related elements: ${JSON.stringify(potentialAssetElements, null, 2)}`)
				}

				// Try to find any clickable element near the current asset display
				const clickableNearAsset = await this.page.$('button, [role="button"], [onclick], [style*="cursor: pointer"]')
				if (clickableNearAsset) {
					const elementInfo = await clickableNearAsset.evaluate(el => ({
						text: el.textContent?.trim(),
						className: el.className,
						id: el.id,
						tagName: el.tagName
					}))
					this.log('info', `Found clickable element that might be asset toggle: ${JSON.stringify(elementInfo)}`)
					assetSelectionToggle = clickableNearAsset
				}
			}

			try {
				if (assetSelectionToggle) {
					await assetSelectionToggle.click()
					this.log('info', 'Clicked asset panel toggle')

					// Wait for the assets block to be visible
					await this.page.waitForSelector(assetsBlock, { state: 'visible', timeout: 5000 })
					await this.page.waitForTimeout(1000)

					// Click on the asset type if it's not already active
					const assetTypeSelector = `.assets-block__nav-item--${assetType}`
					try {
						const assetTypeElement = await this.page.$(assetTypeSelector)
						if (assetTypeElement) {
							// Check if it's already active
							const isActive = await assetTypeElement.evaluate(el =>
								el.classList.contains('assets-block__nav-item--active')
							)
							if (!isActive) {
								await assetTypeElement.click()
								this.log('info', `Clicked asset type: ${assetType}`)
								await this.page.waitForTimeout(1000)
							} else {
								this.log('info', `Asset type ${assetType} is already active`)
							}
						}
					} catch (e) {
						this.log('warn', `Could not find asset type ${assetType}: ${e}`)
					}

					// Select the asset from the list
					const assetListSelector = `.assets-block__alist.alist.alist-${assetType}`
					await this.page.waitForSelector(assetListSelector, { timeout: 5000 })

					// Find the specific asset
					let targetAsset = asset
					if (assetFilter === 'OTC' && !asset.includes('OTC')) {
						targetAsset = `${asset} OTC`
					}

					try {
						// Look for the asset in the list - target the clickable link element
						const assetItem = this.page
							.locator(`${assetListSelector} .alist__item .alist__link`)
							.filter({ hasText: targetAsset })
							.first()

						if (!assetItem) {
							this.log('warn', `Could not find asset ${targetAsset} in the list`)
							return
						}

						// Check if the asset is available (not disabled)
						const isDisabled = await assetItem
							.evaluate(
								el => el.classList.contains('alist__item--no-active') || el.classList.contains('alist__item--no-hover')
							)
							.catch(() => false)

						// Check if the asset is already selected
						const isAlreadySelected = await assetItem
							.evaluate(el => el.classList.contains('alist__item--active'))
							.catch(() => false)

						if (isAlreadySelected) {
							this.log('info', `Asset ${targetAsset} is already selected`)
						} else if (isDisabled) {
							this.log('warn', `Asset ${targetAsset} is not available (disabled)`)
							// Try to find an alternative asset
							const availableAssets = this.page
								.locator(`${assetListSelector} .alist__item:not(.alist__item--no-active) .alist__link`)
								.first()
							if (availableAssets) {
								await this.clickAssetItem(availableAssets)
								const selectedAssetName = await availableAssets.locator('.alist__label').textContent()
								this.log('info', `Selected alternative available asset: ${selectedAssetName}`)
							}
						} else {
							// Click the actual link element, not the list item
							// const assetLink = assetItem.locator('.alist__link').first()
							await this.clickAssetItem(assetItem)
							this.log('info', `Selected asset: ${targetAsset}`)
						}

						await this.page.waitForTimeout(500)

						// Close the asset selection window - try multiple methods
						await this.closeAllModals()
						this.log('info', 'Closed asset selection panel')
					} catch (e) {
						this.log('warn', `Could not find asset ${targetAsset}: ${e}`)
						// Try to select the first available asset as fallback
						try {
							const firstAvailableAsset = this.page
								.locator(`${assetListSelector} .alist__item:not(.alist__item--no-active) .alist__link`)
								.first()
							if (firstAvailableAsset) {
								await this.clickAssetItem(firstAvailableAsset)
								const selectedAssetName = await firstAvailableAsset.locator('.alist__label').textContent()
								this.log('info', `Selected first available asset: ${selectedAssetName}`)
								await this.page.waitForTimeout(500)
								await this.closeAllModals()
								// await this.closeAssetSelectionModal()
							} else {
								this.log('warn', 'No available assets found. Modal is closed.')
							}
						} catch (fallbackError) {
							this.log('warn', `Could not select any asset: ${fallbackError}`)
						}
					}
				} else {
					this.log('warn', 'Could not find asset panel toggle with any known selector')
				}
			} catch (e) {
				this.log('warn', `Could not open asset selection panel: ${e}`)
			}
		} catch (error) {
			this.log('warn', `Could not select trading asset: ${error}`)
		}
	}

	private async waitForTradingInterface(): Promise<void> {
		if (!this.page) return

		try {
			this.log('info', 'Waiting for trading interface to be ready...')

			// Wait for essential trading elements
			const tradingSelectors = ['canvas.layer.plot', '.btn.btn-call, .btn.btn-put', '.block.block--bet-amount']

			// Wait for at least one element from each category
			for (const selectorGroup of tradingSelectors) {
				try {
					await this.page.waitForSelector(selectorGroup, { timeout: 10000 })
					this.log('info', `Found trading element: ${selectorGroup}`)
				} catch (e) {
					this.log('warn', `Trading element not found: ${selectorGroup}`)
				}
			}

			// Additional wait for everything to settle
			await this.page.waitForTimeout(3000)
			this.log('info', 'Trading interface appears to be ready')
		} catch (error) {
			this.log('warn', `Trading interface setup warning: ${error}`)
		}
	}

	private async clickAssetItem(assetItem: any): Promise<void> {
		if (!this.page || !assetItem) return

		try {
			await assetItem.click()
			this.log('info', 'Successfully clicked asset item')
		} catch (error) {
			this.log('warn', `Failed to click asset item with force, trying alternative methods: ${error}`)
		}
	}

	private async closeAllModals(): Promise<void> {
		if (!this.page) return

		try {
			await this.page.keyboard.press('Escape')
			await this.page.waitForTimeout(500)

			await this.page.click('body', { position: { x: 10, y: 10 } })
			await this.page.waitForTimeout(500)
		} catch (e) {}
	}

	/**
	 * Extract price from alternative sources when standard selectors fail
	 */
	private async extractPriceFromAlternativeSources(): Promise<number> {
		if (!this.page) return 0

		try {
			// Method 1: Look for any numeric values that could be prices in the page
			const potentialPrices = await this.page.$$eval('*', elements => {
				const prices: number[] = []

				elements.forEach(el => {
					const text = el.textContent?.trim() || ''
					// Look for decimal numbers that could be prices (between 0.1 and 999999)
					const matches = text.match(/\b\d{1,6}\.\d{2,6}\b/g)
					if (matches) {
						matches.forEach((match: string) => {
							const num = parseFloat(match)
							if (num >= 0.1 && num <= 999999) {
								prices.push(num)
							}
						})
					}
				})

				return prices
			})

			// Filter and find the most likely price (usually the most recent/common one)
			if (potentialPrices.length > 0) {
				// Sort by frequency and pick the most common price
				const priceFreq: { [key: string]: number } = {}
				potentialPrices.forEach(price => {
					const key = price.toFixed(5)
					priceFreq[key] = (priceFreq[key] || 0) + 1
				})

				const mostCommon = Object.entries(priceFreq).sort(([, a], [, b]) => b - a)[0]

				if (mostCommon) {
					const price = parseFloat(mostCommon[0])
					this.log('info', `Extracted price from alternative sources: ${price}`)
					return price
				}
			}

			// Method 2: Try to get price from URL parameters or page data
			const urlPrice = await this.page.evaluate(() => {
				// @ts-ignore - window is available in browser context
				const url = window.location.href
				const priceMatch = url.match(/price=([0-9.]+)/i)
				if (priceMatch) {
					return parseFloat(priceMatch[1])
				}
				return 0
			})

			if (urlPrice > 0) {
				this.log('info', `Extracted price from URL: ${urlPrice}`)
				return urlPrice
			}

			// Method 3: Look for price in meta tags or data attributes
			const metaPrice = await this.page.evaluate(() => {
				// @ts-ignore - document is available in browser context
				const metaTags = document.querySelectorAll('meta[name*="price"], meta[property*="price"]')
				for (const meta of metaTags) {
					const content = meta.getAttribute('content')
					if (content) {
						const price = parseFloat(content)
						if (!isNaN(price) && price > 0) {
							return price
						}
					}
				}
				return 0
			})

			if (metaPrice > 0) {
				this.log('info', `Extracted price from meta tags: ${metaPrice}`)
				return metaPrice
			}

			return 0
		} catch (error) {
			this.log('warn', `Error extracting price from alternative sources: ${error}`)
			return 0
		}
	}

	/**
	 * Extract price from chart canvas or other chart-related elements
	 */
	private async extractPriceFromCanvas(): Promise<number> {
		if (!this.page) return 0

		try {
			// Try to get price from chart-related JavaScript variables or data attributes
			const priceFromJS = await this.page.evaluate(() => {
				// Look for common trading platform price variables
				// @ts-ignore - window is available in browser context
				const win = window as any

				// Check for TradingView widget price
				if (win.TradingView && win.TradingView.widget) {
					try {
						const widget = win.TradingView.widget
						if (widget.chart && widget.chart().getVisibleRange) {
							const range = widget.chart().getVisibleRange()
							if (range && range.to) {
								return range.to
							}
						}
					} catch (e) {
						// Continue
					}
				}

				// Look for price in global variables
				const priceVars = ['currentPrice', 'lastPrice', 'price', 'quote', 'rate']
				for (const varName of priceVars) {
					if (win[varName] && typeof win[varName] === 'number' && win[varName] > 0) {
						return win[varName]
					}
				}

				// Look for price in data attributes on canvas or chart elements
				// @ts-ignore - document is available in browser context
				const canvases = document.querySelectorAll('canvas')
				for (const canvas of canvases) {
					const price = canvas.getAttribute('data-price') || canvas.getAttribute('data-current-price')
					if (price) {
						const numPrice = parseFloat(price)
						if (!isNaN(numPrice) && numPrice > 0) {
							return numPrice
						}
					}
				}

				return 0
			})

			if (priceFromJS > 0) {
				this.log('info', `Extracted price from JavaScript: ${priceFromJS}`)
				return priceFromJS
			}

			// Try to find price in chart container elements
			const chartContainers = await this.page.$$('div[class*="chart"], div[class*="trading"], canvas')
			for (const container of chartContainers) {
				try {
					const priceAttr = await container.getAttribute('data-price')
					if (priceAttr) {
						const price = parseFloat(priceAttr)
						if (!isNaN(price) && price > 0) {
							this.log('info', `Extracted price from chart container: ${price}`)
							return price
						}
					}
				} catch (e) {
					// Continue
				}
			}

			return 0
		} catch (error) {
			this.log('warn', `Error extracting price from canvas: ${error}`)
			return 0
		}
	}

	/**
	 * Extract price from screenshot using OCR as a fallback method
	 */
	private async extractPriceFromScreenshot(): Promise<number> {
		if (!this.screenshotExtractor) {
			this.log('warn', 'Screenshot extractor not initialized')
			return 0
		}

		try {
			const price = await this.screenshotExtractor.extractPriceFromScreenshot()
			if (price > 0) {
				this.log('info', `Extracted price from screenshot: ${price}`)
			}
			return price
		} catch (error) {
			this.log('warn', `Error extracting price from screenshot: ${error}`)
			return 0
		}
	}

	/**
	 * Find and configure optimal price area for screenshot extraction
	 */
	public async calibrateScreenshotExtractor(): Promise<boolean> {
		if (!this.screenshotExtractor) {
			this.log('warn', 'Screenshot extractor not initialized')
			return false
		}

		try {
			this.log('info', 'Calibrating screenshot price extractor...')

			// Enable debug mode during calibration
			const currentConfig = { ...this.screenshotExtractor['config'] } // Access config via indexing
			this.screenshotExtractor.updateConfig({
				debugMode: true,
				screenshotPath: './test-screenshots'
			})

			// First, take a screenshot for debugging
			try {
				if (this.page) {
					const screenshot = await this.page.screenshot({ path: './test-screenshots/calibration_full.png' })
					this.log('info', 'Saved full page screenshot for debugging')
				}
			} catch (error) {
				this.log('warn', `Error saving debug screenshot: ${error}`)
			}

			// Try to find the trading chart first
			const tradingChart = await this.page?.$$('canvas.layer.plot, canvas[class*="chart"]')
			if (tradingChart && tradingChart.length > 0) {
				this.log('info', `Found ${tradingChart.length} chart canvas elements`)
			} else {
				this.log('warn', 'No chart canvas elements found - this may affect calibration')
			}

			// Find and configure optimal price area
			const optimalArea = await this.screenshotExtractor.findOptimalPriceArea()

			if (optimalArea) {
				this.screenshotExtractor.updateConfig({
					priceArea: optimalArea,
					debugMode: true // Keep debug on for now to verify it works
				})

				// Verify that the configured area works
				const testPrice = await this.extractPriceFromScreenshot()
				if (testPrice > 0) {
					this.log(
						'info',
						`Screenshot extractor calibrated successfully with price area: ${JSON.stringify(optimalArea)}`
					)
					this.log('info', `Test price extracted: ${testPrice}`)
					return true
				} else {
					this.log('warn', 'Calibration found an area but extraction test failed')
					// Try to debug why extraction failed
					await this.debugPriceElements()
				}
			} else {
				this.log('warn', 'Could not find optimal price area for screenshot extraction')
				// Fall back to browser size-based defaults
				if (this.page) {
					const viewport = await this.page.evaluate(() => {
						return {
							// @ts-ignore - window is available in browser context
							width: window.innerWidth,
							// @ts-ignore - window is available in browser context
							height: window.innerHeight
						}
					})

					// Default price area based on viewport size
					const defaultArea = {
						x: Math.max(viewport.width - 300, 800),
						y: 100,
						width: 250,
						height: 60
					}

					this.log('info', `Using default price area based on viewport size: ${JSON.stringify(defaultArea)}`)
					this.screenshotExtractor.updateConfig({ priceArea: defaultArea })
					return true
				}
			}

			return false
		} catch (error) {
			this.log('error', `Error calibrating screenshot extractor: ${error}`)
			return false
		}
	}

	public async debugPriceElements(): Promise<void> {
		if (!this.page) return

		try {
			// Only run debug once every 30 seconds to avoid spam
			const now = Date.now()
			if (this.lastDebugTime && now - this.lastDebugTime < 30000) return
			this.lastDebugTime = now

			this.log('info', 'Debugging: Analyzing page for price elements...')

			// Check current URL
			const currentUrl = this.page.url()
			this.log('info', `Current page URL: ${currentUrl}`)

			// Check if we're on the right page
			const isOnTradingPage =
				currentUrl.includes('pocketoption.com') &&
				(currentUrl.includes('cabinet') || currentUrl.includes('demo') || currentUrl.includes('trading'))

			if (!isOnTradingPage) {
				this.log('warn', 'Not on Pocket Option trading page. Please navigate to the trading interface.')
				return
			}

			// Check for trading chart
			const chartElements = await this.page.$$('canvas.layer.plot, canvas[class*="chart"], canvas.trading-chart')
			if (chartElements.length > 0) {
				this.log('info', `Found ${chartElements.length} chart canvas elements`)

				// Get chart dimensions
				for (let i = 0; i < Math.min(chartElements.length, 3); i++) {
					const chartInfo = await chartElements[i].boundingBox()
					if (chartInfo) {
						this.log(
							'info',
							`Chart ${i + 1} dimensions: x=${Math.round(chartInfo.x)}, y=${Math.round(
								chartInfo.y
							)}, width=${Math.round(chartInfo.width)}, height=${Math.round(chartInfo.height)}`
						)
					}
				}
			} else {
				this.log('warn', 'No chart canvas elements found - this may affect price extraction')
			}

			// Look for price specific elements with better selectors
			const priceSpecificSelectors = [
				'[data-price]',
				'[data-current-price]',
				'[class*="price"][class*="current"]',
				'[class*="rate"][class*="live"]',
				'.current-price',
				'.price-value',
				'.asset-price',
				'.quote-value'
			]

			let foundPriceElement = false
			for (const selector of priceSpecificSelectors) {
				const elements = await this.page.$$(selector)
				if (elements.length > 0) {
					this.log('info', `Found ${elements.length} elements matching selector: ${selector}`)

					// Check content of first few elements
					for (let i = 0; i < Math.min(elements.length, 3); i++) {
						const text = await elements[i].evaluate(el => el.textContent?.trim() || '')
						const box = await elements[i].boundingBox()
						if (box) {
							this.log(
								'info',
								`  Element ${i + 1} content: "${text}", position: x=${Math.round(box.x)}, y=${Math.round(
									box.y
								)}, width=${Math.round(box.width)}, height=${Math.round(box.height)}`
							)
						} else {
							this.log('info', `  Element ${i + 1} content: "${text}" (no position info)`)
						}
					}

					foundPriceElement = true
				}
			}

			if (!foundPriceElement) {
				this.log('warn', 'No price-specific elements found with standard selectors')
			}

			// Look for elements with numeric content (potential prices)
			const potentialPriceElements = await this.page.$$eval('*', elements => {
				return elements
					.filter(el => {
						const text = el.textContent?.trim() || ''
						// Look for price-like patterns
						return /^\d+\.\d{2,6}$/.test(text) && text.length >= 3 && text.length <= 10
					})
					.slice(0, 15) // Increased limit to see more potential elements
					.map(el => ({
						text: el.textContent?.trim(),
						tagName: el.tagName,
						className: el.className,
						id: el.id,
						parentClass: el.parentElement?.className || '',
						rect: el.getBoundingClientRect
							? {
									x: Math.round(el.getBoundingClientRect().left),
									y: Math.round(el.getBoundingClientRect().top),
									width: Math.round(el.getBoundingClientRect().width),
									height: Math.round(el.getBoundingClientRect().height)
							  }
							: null
					}))
			})

			if (potentialPriceElements.length > 0) {
				this.log('info', `Found ${potentialPriceElements.length} potential price elements with price-like text:`)
				potentialPriceElements.forEach((el, index) => {
					if (el.rect) {
						this.log(
							'info',
							`  ${index + 1}. "${el.text}" (${el.tagName}.${el.className}) at x=${el.rect.x}, y=${el.rect.y}`
						)
					} else {
						this.log('info', `  ${index + 1}. "${el.text}" (${el.tagName}.${el.className})`)
					}
				})
			} else {
				this.log('warn', 'No potential price elements found with price-like text')
			}

			// Show information about screenshot extractor if available
			if (this.screenshotExtractor) {
				this.log('info', 'Screenshot extractor status:')
				// Access private config (not ideal but necessary for debugging)
				const config = this.screenshotExtractor['config']
				if (config.priceArea) {
					this.log(
						'info',
						`  Price area: x=${config.priceArea.x}, y=${config.priceArea.y}, width=${config.priceArea.width}, height=${config.priceArea.height}`
					)

					// Take and save a debug screenshot of the price area
					try {
						await this.page.screenshot({
							path: './test-screenshots/debug_full.png'
						})
						this.log('info', '  Saved debug screenshot to ./test-screenshots/debug_full.png')
					} catch (e) {
						this.log('warn', `  Error saving debug screenshot: ${e}`)
					}
				} else {
					this.log('warn', '  Price area not configured')
				}

				// Try price extraction
				try {
					const price = await this.extractPriceFromScreenshot()
					if (price > 0) {
						this.log('info', `  Screenshot price extraction test: SUCCESS (${price})`)
					} else {
						this.log('warn', '  Screenshot price extraction test: FAILED (returned 0)')
					}
				} catch (error) {
					this.log('error', `  Screenshot price extraction test: ERROR (${error})`)
				}
			} else {
				this.log('warn', 'Screenshot extractor not initialized')
			}

			// Check page load state
			const pageLoadState = await this.page.evaluate(() => {
				// @ts-ignore - document is available in browser context
				return document.readyState
			})
			this.log('info', `Page load state: ${pageLoadState}`)
		} catch (error) {
			this.log('warn', `Debug error: ${error}`)
		}
	}

	private lastDebugTime?: number
	private lastPriceWarningTime?: number
}
